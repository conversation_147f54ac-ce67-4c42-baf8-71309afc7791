/*
  # Create desks table

  1. New Tables
    - `desks`
      - `id` (uuid, primary key)
      - `name` (text, not null)
      - `status` (text with check constraint, default 'available')
      - `zone_id` (uuid, foreign key to zones)
      - `coordinates` (jsonb for x,y coordinates)
      - `created_at` (timestamptz with default)
      - `updated_at` (timestamptz with default)

  2. Security
    - Enable RLS on `desks` table
    - Add policy for authenticated users to read all desks
    - Add policy for admins to manage desks

  3. Constraints
    - Foreign key relationship to zones table
    - Check constraint for valid status values
    - Index on zone_id for performance

  4. Triggers
    - Add trigger to automatically update `updated_at` timestamp
*/

-- Create desks table
CREATE TABLE IF NOT EXISTS desks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  status text DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'maintenance')),
  zone_id uuid NOT NULL REFERENCES zones(id) ON DELETE CASCADE,
  coordinates jsonb NOT NULL DEFAULT '{"x": 0, "y": 0}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_desks_zone_id ON desks(zone_id);
CREATE INDEX IF NOT EXISTS idx_desks_status ON desks(status);

-- Enable RLS
ALTER TABLE desks ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Authenticated users can read desks"
  ON desks
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage desks"
  ON desks
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- Create trigger for updated_at
CREATE TRIGGER update_desks_updated_at
  BEFORE UPDATE ON desks
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();