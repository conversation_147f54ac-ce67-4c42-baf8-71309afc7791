/*
  # Update trigger to handle manager_id properly

  1. Problem
    - Manager selection during signup gets lost during email confirmation
    - <PERSON><PERSON> needs to extract manager_id from auth metadata

  2. Solution
    - Store manager_id in auth metadata during signup
    - Update trigger to extract and handle manager_id properly
    - Handle null values correctly (when "none" is selected)

  3. Flow
    - User selects manager during signup
    - Manager_id stored in auth metadata
    - When user confirms email, trigger extracts manager_id and assigns it
*/

-- Update function to handle new user creation with manager_id from metadata
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, role, manager_id)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'employee'),
    CASE 
      WHEN NEW.raw_user_meta_data->>'manager_id' IS NULL THEN NULL
      WHEN NEW.raw_user_meta_data->>'manager_id' = 'null' THEN NULL
      WHEN NEW.raw_user_meta_data->>'manager_id' = '' THEN NULL
      WHEN NEW.raw_user_meta_data->>'manager_id' = 'none' THEN NULL
      ELSE (NEW.raw_user_meta_data->>'manager_id')::uuid
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 