-- Verification script to check if database functions were updated correctly

-- 1. Check if functions exist
SELECT 
    proname as function_name,
    prosrc as function_source
FROM pg_proc 
WHERE proname IN ('execute_query', 'execute_query_with_rls')
ORDER BY proname;

-- 2. Test the execute_query function with a simple query that has a semicolon
SELECT execute_query('SELECT 1 as test_value;');

-- 3. Test without semicolon
SELECT execute_query('SELECT 1 as test_value');

-- 4. Check if the rate limiting table exists and has the right structure
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'ai_query_rate_limits'
ORDER BY ordinal_position;

-- 5. Check if the audit table exists
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name = 'ai_query_audit'
ORDER BY ordinal_position;

-- 6. Test a more complex query with semicolon (like what <PERSON> generates)
SELECT execute_query('
SELECT 
    ''Engineering'' as department,
    COUNT(*) as total_bookings
FROM users 
WHERE department IS NOT NULL
GROUP BY department
ORDER BY total_bookings DESC;
'); 