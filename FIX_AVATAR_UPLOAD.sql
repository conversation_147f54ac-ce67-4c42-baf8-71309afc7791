-- ===================================
-- FIX AVATAR UPLOAD RLS ERRORS
-- Run this in your Supabase SQL Editor
-- ===================================

-- Drop any existing problematic policies
DROP POLICY IF EXISTS "Avatar images are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their own avatar" ON storage.objects;
DROP POLICY IF EXISTS "Users can upload avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can update avatars" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete avatars" ON storage.objects;

-- Create simple, permissive policies for avatars
CREATE POLICY "Public Avatar Access" ON storage.objects 
FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Authenticated Avatar Upload" ON storage.objects 
FOR INSERT WITH CHECK (
  bucket_id = 'avatars' AND 
  auth.role() = 'authenticated'
);

CREATE POLICY "Authenticated Avatar Update" ON storage.objects 
FOR UPDATE USING (
  bucket_id = 'avatars' AND 
  auth.role() = 'authenticated'
);

CREATE POLICY "Authenticated Avatar Delete" ON storage.objects 
FOR DELETE USING (
  bucket_id = 'avatars' AND 
  auth.role() = 'authenticated'
);

-- Ensure the bucket exists and is public
UPDATE storage.buckets 
SET public = true 
WHERE id = 'avatars';

-- Check if bucket exists, create if not
INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Verify setup
SELECT 
  'Bucket exists' as check_type,
  EXISTS(SELECT 1 FROM storage.buckets WHERE id = 'avatars') as result
UNION ALL
SELECT 
  'Bucket is public' as check_type,
  (SELECT public FROM storage.buckets WHERE id = 'avatars') as result; 