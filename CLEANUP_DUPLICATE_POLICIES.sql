-- ===================================
-- CLEANUP DUPLICATE AND PROBLEMATIC POLICIES
-- Run this to remove old policies that might still cause recursion
-- ===================================

-- Remove old problematic policies on users table
DROP POLICY IF EXISTS "Admins can update all users" ON users;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON users;
DROP POLICY IF EXISTS "Allow all authenticated users to view users" ON users;
DROP POLICY IF EXISTS "Allow authenticated inserts" ON users;
DROP POLICY IF EXISTS "Users can update own data" ON users;

-- Keep only these safe policies on users table:
-- "admin_select_all" - uses security definer function
-- "admin_update_all" - uses security definer function  
-- "manager_select_team" - uses security definer function
-- "users_insert_own" - simple auth.uid() = id check
-- "users_select_own" - simple auth.uid() = id check
-- "users_update_own" - simple auth.uid() = id check

-- Verify the final policies (should only see the 6 safe ones)
SELECT 
  'Cleanup Complete' as status,
  COUNT(*) as remaining_user_policies
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public';

-- Show remaining policies
SELECT 
  tablename,
  policyname,
  cmd,
  'SAFE - No recursion' as safety_status
FROM pg_policies 
WHERE tablename = 'users' AND schemaname = 'public'
ORDER BY policyname; 