import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BookingWithDetails } from '@/types';
import { format } from 'date-fns';

interface RecentBookingsProps {
  bookings: BookingWithDetails[];
}

export function RecentBookings({ bookings }: RecentBookingsProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'booked':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'checked-in':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  return (
    <Card className="shadow-clay-sm">
      <CardHeader>
        <CardTitle>Recent Bookings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {bookings.length === 0 ? (
            <p className="text-sm text-muted-foreground text-center py-4">
              No recent bookings found
            </p>
          ) : (
            bookings.map((booking) => (
              <div key={booking.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg border border-border/50 hover:border-border transition-colors">
                <div>
                  <div className="font-medium">{booking.desk.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {booking.desk.zone.name} • Floor {booking.desk.zone.floor_number}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {booking.date === booking.end_date 
                      ? format(new Date(booking.date), 'MMM dd, yyyy')
                      : `${format(new Date(booking.date), 'MMM dd')} - ${format(new Date(booking.end_date), 'MMM dd, yyyy')}`
                    }
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {new Date(`2000-01-01 ${booking.start_time}`).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit',
                      hour12: true 
                    })} - {new Date(`2000-01-01 ${booking.end_time}`).toLocaleTimeString([], { 
                      hour: '2-digit', 
                      minute: '2-digit',
                      hour12: true 
                    })}
                  </div>
                </div>
                <Badge className={getStatusColor(booking.status)}>
                  {booking.status}
                </Badge>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}