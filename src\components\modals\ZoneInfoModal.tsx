import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Building, Users, Calendar, Camera } from 'lucide-react';
import { Zone } from '@/types';
import { format } from 'date-fns';

interface ZoneInfoModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  zone: Zone & { desks?: { count: number }[] } | null;
}

export function ZoneInfoModal({ isOpen, onOpenChange, zone }: ZoneInfoModalProps) {
  if (!zone) return null;

  const deskCount = zone.desks?.[0]?.count || 0;

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[700px] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            <MapPin className="h-4 w-4 md:h-5 md:w-5 text-primary" />
            Zone Information
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 md:space-y-6 px-1">
          {/* Header Section with Photo */}
          <div className="relative">
            {zone.photo ? (
              <div className="relative h-32 md:h-48 w-full overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-purple-600">
                <img 
                  src={zone.photo} 
                  alt={zone.name}
                  className="h-full w-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
                <div className="absolute bottom-2 left-2 md:bottom-4 md:left-4 text-white">
                  <h2 className="text-lg md:text-2xl font-bold">{zone.name}</h2>
                  <div className="flex items-center gap-2 mt-1">
                    <Building className="h-3 w-3 md:h-4 md:w-4" />
                    <span className="text-xs md:text-sm">Floor {zone.floor_number}</span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="relative h-32 md:h-48 w-full overflow-hidden rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                <div className="text-center text-white">
                  <Camera className="h-8 w-8 md:h-12 md:w-12 mx-auto mb-2 md:mb-3 opacity-60" />
                  <h2 className="text-lg md:text-2xl font-bold">{zone.name}</h2>
                  <div className="flex items-center justify-center gap-2 mt-1">
                    <Building className="h-3 w-3 md:h-4 md:w-4" />
                    <span className="text-xs md:text-sm">Floor {zone.floor_number}</span>
                  </div>
                </div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
              </div>
            )}
          </div>

          {/* Information Cards */}
          <div className="grid grid-cols-3 gap-2 md:gap-4">
            <Card className="bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700">
              <CardContent className="p-2 md:p-4 text-center">
                <Building className="h-5 w-5 md:h-8 md:w-8 mx-auto mb-1 md:mb-2 text-blue-600 dark:text-blue-400" />
                <div className="text-lg md:text-2xl font-bold text-blue-900 dark:text-blue-100">
                  Floor {zone.floor_number}
                </div>
                <div className="text-xs md:text-sm text-blue-600 dark:text-blue-300">
                  Floor Level
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700">
              <CardContent className="p-2 md:p-4 text-center">
                <Users className="h-5 w-5 md:h-8 md:w-8 mx-auto mb-1 md:mb-2 text-green-600 dark:text-green-400" />
                <div className="text-lg md:text-2xl font-bold text-green-900 dark:text-green-100">
                  {deskCount}
                </div>
                <div className="text-xs md:text-sm text-green-600 dark:text-green-300">
                  Available Desks
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-700">
              <CardContent className="p-2 md:p-4 text-center">
                <Calendar className="h-5 w-5 md:h-8 md:w-8 mx-auto mb-1 md:mb-2 text-purple-600 dark:text-purple-400" />
                <div className="text-xs font-semibold text-purple-900 dark:text-purple-100">
                  {format(new Date(zone.created_at), 'MMM dd, yyyy')}
                </div>
                <div className="text-xs md:text-sm text-purple-600 dark:text-purple-300 mt-1">
                  Created
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Description Section */}
          {zone.description && (
            <Card>
              <CardContent className="p-3 md:p-6">
                <h3 className="text-base md:text-lg font-semibold mb-2 md:mb-3 flex items-center gap-2">
                  <MapPin className="h-4 w-4 md:h-5 md:w-5 text-muted-foreground" />
                  Description
                </h3>
                <p className="text-sm md:text-base text-muted-foreground leading-relaxed">
                  {zone.description}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Zone Status */}
          <div className="flex items-center justify-center">
            <Badge 
              variant="secondary" 
              className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 md:px-4 md:py-2"
            >
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
              <span className="text-xs md:text-sm">Zone Active</span>
            </Badge>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 