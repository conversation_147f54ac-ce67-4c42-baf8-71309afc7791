# SymDesk User Manual

Welcome to SymDesk! This guide will walk you through creating your account, managing your profile, and understanding the different roles within the system.

## 1. 🚀 Getting Started: Creating Your Account

Signing up for SymDesk is a simple process. Follow these steps to create your account and get started.

### Step 1: Fill Out the Registration Form
1.  **Navigate to the Sign-Up Page**: Your administrator will provide you with the link to the SymDesk portal.
2.  **Enter Your Details**:
    *   **Full Name**: Your first and last name.
    *   **Email**: You must use your official `@symplexity.co.za` email address.
    *   **Password**: Choose a secure password that is at least 6 characters long.
    *   **Manager (Optional)**: If you report to a manager, you can select them from the dropdown list. This helps organize teams within SymDesk. If you don't have a manager or are unsure, you can leave this as "No manager".

    ![Screenshot of the SymDesk registration form with fields for name, email, manager, and password.](https://i.imgur.com/example.png)

### Step 2: Confirm Your Email
1.  **Check Your Inbox**: After submitting the form, you will receive a confirmation email from Supabase (our backend provider).
2.  **Click the Confirmation Link**: Open the email and click the link inside to verify your email address. This is a crucial security step.
3.  **Return and Sign In**: Once your email is confirmed, you can return to the SymDesk portal and sign in with your credentials.

## 2. 👤 Managing Your Profile

A complete profile helps your colleagues get to know you and makes the office experience better for everyone. We highly encourage all users to update their profiles after their first login.

### How to Update Your Profile
1.  **Navigate to the Profile Page**: Click on your name or avatar in the sidebar to go to your profile.
2.  **Click "Edit Profile"**: This will allow you to change your personal information.
3.  **Fill in Your Details**:
    *   **Name, Department, Location, Phone**: Keep this information up-to-date.
    *   **Bio**: Add a short bio to introduce yourself to your colleagues.
4.  **Upload a Profile Picture (Avatar)**:
    *   Click the **camera icon** on your avatar.
    *   Choose an image file from your computer (JPG, PNG, GIF).
    *   The image will be uploaded and set as your new profile picture. A professional-looking avatar helps create a friendly and recognizable presence in the app!

    ![Screenshot of the user profile page showing the avatar, user details, and the 'Edit Profile' button.](https://i.imgur.com/example2.png)

5.  **Save Changes**: Click the "Save" button to apply your updates.

## 3. 🧑‍💼 The Manager Role: Features & Responsibilities

Users with the 'Manager' role have additional permissions and tools to help them oversee their teams' office usage and well-being.

### What Managers Can See and Do

1.  **Team Dashboard**:
    *   Managers have a dedicated "Manage Team" page.
    *   This dashboard provides a comprehensive overview of all direct reports.

2.  **View Team Member Profiles**:
    *   Managers can view the profiles of employees who have selected them as their manager.
    *   This includes their contact details, department, and bio.

3.  **Monitor Team Bookings**:
    *   The core feature for managers is the ability to see all past and upcoming desk bookings for each of their team members.
    *   This helps in coordinating team days in the office, ensuring space is available, and understanding office usage patterns.

    ![Screenshot of the 'Manage Team' dashboard, showing a list of team members with their booking stats.](https://i.imgur.com/example3.png)

4.  **Booking Analytics**:
    *   Managers can see key statistics for each team member, including:
        *   **Total & Upcoming Bookings**: Get a quick glance at office attendance.
        *   **Favorite Desk/Zone**: Understand employee preferences for office layout planning.
        *   **Recent Activity**: See when the last booking was made.

### How Manager Permissions Work
*   **Data Privacy**: A manager can **only** see information related to users who are their direct reports (`manager_id` is set to the manager's ID). They cannot see the data of other employees or other managers' teams.
*   **Admin Oversight**: Admin users have a similar view but can see all users and teams across the organization for administrative purposes.

This concludes the SymDesk User Manual. If you have any questions, please reach out to your system administrator.
