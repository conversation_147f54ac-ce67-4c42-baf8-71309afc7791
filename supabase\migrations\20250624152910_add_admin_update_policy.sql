/*
  # Add admin update policy for users table

  1. Policy
    - Allow admin users to update any user's data (including roles)
*/

-- Add policy for admin users to update all user data
CREATE POLICY "Admin users can update all user data"
  ON users
  FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.id = auth.uid()
      AND users.role = 'admin'
    )
  ); 