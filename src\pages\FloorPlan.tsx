import { useState, useEffect } from 'react';
import { useFloorPlanData } from '@/hooks/useFloorPlanData';
import { useQueryClient } from '@tanstack/react-query';
import { DeskWithZone } from '@/types';
import { CalendarView } from '@/components/calendar/CalendarView';
import { FloorPlanView } from '@/components/floorplan/FloorPlanView';
import { DeskDetailsModal } from '@/components/modals/DeskDetailsModal';
import { BookingDialog } from '@/components/modals/BookingDialog';
import { DayBookingsModal } from '@/components/modals/DayBookingsModal';
import { AvailableDesksModal } from '@/components/modals/AvailableDesksModal';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { User, Grid, Calendar as CalendarIcon, MapPin, Timer, X, LogIn, CheckCircle2, Clock, Download } from 'lucide-react';
import { format, startOfDay, addDays, addWeeks, isBefore, isAfter, isToday, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';

import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import { generateICSFile, downloadICSFile } from '@/lib/calendar';
import { useAuth } from '@/contexts/AuthContext';

// Generate time options in 30-minute intervals
const generateTimeOptions = () => {
  const times = [];
  for (let hour = 8; hour <= 18; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      const displayTime = new Date(`2000-01-01 ${timeString}`).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true 
      });
      times.push({ value: timeString, label: displayTime });
    }
  }
  return times;
};

const timeOptions = generateTimeOptions();

export function FloorPlan() {
  const { appUser } = useAuth();
  const queryClient = useQueryClient();
  const [viewMode, setViewMode] = useState<'floor' | 'calendar'>('floor');
  const [selectedDesk, setSelectedDesk] = useState<DeskWithZone | null>(null);
  const [selectedDay, setSelectedDay] = useState<Date | null>(null);
  const [isDeskDetailsModalOpen, setIsDeskDetailsModalOpen] = useState(false);
  const [isBookingDialogOpen, setIsBookingDialogOpen] = useState(false);
  const [isDayBookingsModalOpen, setIsDayBookingsModalOpen] = useState(false);
  const [isAvailableDesksModalOpen, setIsAvailableDesksModalOpen] = useState(false);

  // Booking form state
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();
  const [startTime, setStartTime] = useState('08:00');
  const [endTime, setEndTime] = useState('17:00');
  const [isStartCalendarOpen, setIsStartCalendarOpen] = useState(false);
  const [isEndCalendarOpen, setIsEndCalendarOpen] = useState(false);
  const [selectedFloor, setSelectedFloor] = useState<string>('all');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [isDateCalendarOpen, setIsDateCalendarOpen] = useState(false);

  // Update currentMonth when selectedDate changes to a different month (for floor plan view)
  const handleSelectedDateChange = (date: Date) => {
    setSelectedDate(date);
    
    // If the selected date is in a different month, update currentMonth to fetch the right data
    if (date.getFullYear() !== currentMonth.getFullYear() || 
        date.getMonth() !== currentMonth.getMonth()) {
      setCurrentMonth(date);
    }
  };

  const {
    desks,
    userBookings,
    monthlyBookings,
    currentMonth,
    setCurrentMonth,
    isLoading,
    useExistingBookings,
    getNextBookingsForDesk,
    getCurrentBooking,
    getRealtimeStatus,
    getBookingsForDay,
    getBookingCountForDay,
    getAvailableDesksForDay,
    getAvailableCountForDay,
    getTotalDeskCount,
    createBookingMutation,
    deleteBookingMutation,
  } = useFloorPlanData();



  // Real-time data refresh - refetch every 30 seconds
  useEffect(() => {
    const refreshData = () => {
      queryClient.invalidateQueries({ queryKey: ['desks-with-zones'] });
      queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
    };

    // Refresh on component mount
    refreshData();

    // Set up interval for periodic refresh
    const interval = setInterval(refreshData, 30000); // 30 seconds

    // Refresh when window regains focus (user comes back to tab)
    const handleFocus = () => {
      refreshData();
    };
    
    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, [queryClient]);

  // Process data for floor plan view
  const groupedData = desks?.reduce((acc: Record<number, Record<string, { zone: any; desks: DeskWithZone[] }>>, desk) => {
    const floorNumber = desk.zone.floor_number;
    const zoneId = desk.zone.id;
    
    if (!acc[floorNumber]) {
      acc[floorNumber] = {};
    }
    
    if (!acc[floorNumber][zoneId]) {
      acc[floorNumber][zoneId] = {
        zone: desk.zone,
        desks: []
      };
    }
    
    acc[floorNumber][zoneId].desks.push(desk);
    return acc;
  }, {}) || {};

  // Helper functions for booking display
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'booked':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'checked-in':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'booked':
        return <Clock className="h-4 w-4" />;
      case 'checked-in':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'cancelled':
        return <X className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (startDate === endDate) {
      return format(start, 'MMM dd, yyyy');
    } else {
      const daysDiff = differenceInDays(end, start) + 1;
      return `${format(start, 'MMM dd')} - ${format(end, 'MMM dd, yyyy')} (${daysDiff} days)`;
    }
  };

  const getBookingDuration = (startTime: string, endTime: string, startDate: string, endDate: string) => {
    if (startDate === endDate) {
      // Same day booking - calculate hours
      const start = new Date(`2000-01-01 ${startTime}`);
      const end = new Date(`2000-01-01 ${endTime}`);
      const hours = differenceInHours(end, start);
      const minutes = differenceInMinutes(end, start) % 60;
      
      if (hours === 0) {
        return `${minutes}m`;
      } else if (minutes === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h ${minutes}m`;
      }
    } else {
      // Multi-day booking
      const days = differenceInDays(new Date(endDate), new Date(startDate)) + 1;
      return `${days} day${days > 1 ? 's' : ''}`;
    }
  };

  const canCheckIn = (booking: any) => {
    return booking.status === 'booked' && isToday(new Date(booking.date));
  };

  const canCancel = (booking: any) => {
    // Can cancel if booking is not checked-in/cancelled and the booking hasn't ended yet
    return booking.status === 'booked' && new Date(booking.end_date) >= new Date();
  };

  // Get nearest 3 upcoming bookings from the selected date
  const nearestBookings = userBookings?.filter(b => 
    new Date(b.date) >= startOfDay(selectedDate) && b.status !== 'cancelled'
  ).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()).slice(0, 3) || [];

  // Sort desks within each zone numerically
  if (groupedData) {
    Object.values(groupedData).forEach(floor => {
      Object.values(floor).forEach(zone => {
        zone.desks.sort((a, b) => {
          // Extract the number from desk names (e.g., "A10" -> 10, "Desk A10" -> 10)
          const getNumber = (name: string) => {
            const match = name.match(/(\d+)/);
            return match ? parseInt(match[1], 10) : 0;
          };
          
          const numA = getNumber(a.name);
          const numB = getNumber(b.name);
          
          // If numbers are the same, fall back to alphabetical sorting
          if (numA === numB) {
            return a.name.localeCompare(b.name);
          }
          
          return numA - numB;
        });
      });
    });
  }

  const availableFloors = Object.keys(groupedData).map(Number).sort();

  // Fetch existing bookings for the selected desk
  const { data: existingBookings } = useExistingBookings(selectedDesk?.id, startDate, endDate);

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'occupied':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'maintenance':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatTime = (timeString: string) => {
    const time = new Date(`2000-01-01 ${timeString}`);
    return time.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const isDateDisabled = (date: Date) => {
    const today = startOfDay(new Date());
    const maxDate = addDays(today, 90); // Can book up to 90 days in advance
    
    return isBefore(date, today) || isAfter(date, maxDate);
  };

  const formatDateToLocal = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const getStatusForDate = (desk: DeskWithZone, date: Date) => {
    // If it's today, use real-time status
    const today = new Date();
    const isToday = formatDateToLocal(date) === formatDateToLocal(today);
    
    if (isToday) {
      return getRealtimeStatus(desk);
    }
    
    // For other dates, check if desk is booked
    const dayBookings = getBookingsForDay(date);
    const isBooked = dayBookings.some(booking => booking.desk_id === desk.id);
    
    if (isBooked) {
      return 'occupied';
    }
    
    // Check desk's base status (maintenance, etc.)
    if (desk.status === 'maintenance') {
      return 'maintenance';
    }
    
    return 'available';
  };

  const getBookingForDate = (desk: DeskWithZone, date: Date) => {
    // If it's today, check current booking
    const today = new Date();
    const isToday = formatDateToLocal(date) === formatDateToLocal(today);
    
    if (isToday) {
      const currentBooking = getCurrentBooking(desk.id);
      return currentBooking || null;
    }
    
    // For other dates, get booking from day bookings
    const dayBookings = getBookingsForDay(date);
    const booking = dayBookings.find(booking => booking.desk_id === desk.id);
    return booking || null;
  };

  const hasTimeConflict = () => {
    if (!existingBookings || !startDate || !endDate) return false;
    
    const bookingStartDate = formatDateToLocal(startDate);
    const bookingEndDate = formatDateToLocal(endDate);
    
    return existingBookings.some(booking => {
      // Check if dates overlap
      const existingStart = booking.date;
      const existingEnd = booking.end_date;
      
      // Check for date overlap
      const datesOverlap = (
        (bookingStartDate <= existingEnd) && (bookingEndDate >= existingStart)
      );
      
      if (!datesOverlap) return false;
      
      // If dates overlap, check for time overlap
      const bookingStart = parseInt(startTime.replace(':', ''));
      const bookingEnd = parseInt(endTime.replace(':', ''));
      const existingStartTime = parseInt(booking.start_time.replace(':', ''));
      const existingEndTime = parseInt(booking.end_time.replace(':', ''));
      
      return (bookingStart < existingEndTime) && (bookingEnd > existingStartTime);
    });
  };

  const hasUserBookingConflict = () => {
    if (!userBookings || !startDate || !endDate) return false;
    
    const bookingStartDate = formatDateToLocal(startDate);
    const bookingEndDate = formatDateToLocal(endDate);
    
    return userBookings.some(booking => {
      // Check if dates overlap
      const existingStart = booking.date;
      const existingEnd = booking.end_date;
      
      // Check for date overlap
      const datesOverlap = (
        (bookingStartDate <= existingEnd) && (bookingEndDate >= existingStart)
      );
      
      if (!datesOverlap) return false;
      
      // If dates overlap, check for time overlap
      const bookingStart = parseInt(startTime.replace(':', ''));
      const bookingEnd = parseInt(endTime.replace(':', ''));
      const existingStartTime = parseInt(booking.start_time.replace(':', ''));
      const existingEndTime = parseInt(booking.end_time.replace(':', ''));
      
      return (bookingStart < existingEndTime) && (bookingEnd > existingStartTime);
    });
  };

  const isValidTimeRange = () => {
    return startTime < endTime;
  };

  const isValidDateRange = () => {
    if (!startDate || !endDate) return false;
    return startDate <= endDate;
  };

  const canSubmitBooking = (): boolean => {
    return !!(
      selectedDesk &&
      startDate &&
      endDate &&
      startTime &&
      endTime &&
      isValidTimeRange() &&
      isValidDateRange() &&
      !hasTimeConflict() &&
      !hasUserBookingConflict()
    );
  };

  const handleBookDesk = async (downloadCalendar?: boolean, recurringWeeks?: number) => {
    if (!canSubmitBooking()) return;

    try {
      if (recurringWeeks && recurringWeeks > 1) {
        // Handle recurring bookings
        const bookingPromises = [];

        for (let i = 0; i < recurringWeeks; i++) {
          const weekStartDate = addWeeks(startDate!, i);
          const weekEndDate = addWeeks(endDate!, i);

          bookingPromises.push(
            supabase
              .from('bookings')
              .insert({
                user_id: appUser!.id,
                desk_id: selectedDesk!.id,
                date: formatDateToLocal(weekStartDate),
                end_date: formatDateToLocal(weekEndDate),
                start_time: startTime,
                end_time: endTime,
                status: 'booked'
              })
          );
        }

        const results = await Promise.all(bookingPromises);

        // Check if any booking failed
        const failedBookings = results.filter(result => result.error);
        if (failedBookings.length > 0) {
          const successCount = results.length - failedBookings.length;
          if (successCount > 0) {
            toast.success(`${successCount} of ${recurringWeeks} bookings created successfully`);
            toast.error(`${failedBookings.length} bookings failed - some dates may already be booked`);
          } else {
            toast.error('All recurring bookings failed - dates may already be booked');
            return;
          }
        } else {
          toast.success(`${recurringWeeks} recurring bookings created successfully!`);
        }

        // Invalidate queries to refresh data
        queryClient.invalidateQueries({ queryKey: ['desk-bookings'] });
        queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
        queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
        queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
        queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
        queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
      } else {
        // Handle single booking - use the original mutation which has proper success handling
        createBookingMutation.mutate({
          deskId: selectedDesk!.id,
          startDate: formatDateToLocal(startDate!),
          endDate: formatDateToLocal(endDate!),
          startTime,
          endTime,
          downloadCalendar,
        }, {
          onSuccess: () => {
            // Close dialog and reset form on successful booking
            setIsBookingDialogOpen(false);
            setSelectedDesk(null);
            setStartDate(undefined);
            setEndDate(undefined);
            setStartTime('08:00');
            setEndTime('17:00');
          }
        });
        return; // Exit early for single booking
      }

      // Close dialog and reset form on successful recurring booking (this only runs for recurring)
      setIsBookingDialogOpen(false);
      setSelectedDesk(null);
      setStartDate(undefined);
      setEndDate(undefined);
      setStartTime('08:00');
      setEndTime('17:00');

    } catch (error: any) {
      toast.error('Failed to create recurring bookings: ' + error.message);
    }
  };

  const openBookingDialogFromModal = () => {
    setIsDeskDetailsModalOpen(false);
    
    // When coming from floor plan view, pre-populate with the selected date
    if (viewMode === 'floor') {
      setStartDate(selectedDate);
      setEndDate(selectedDate);
    }
    
    setIsBookingDialogOpen(true);
  };

  const handleDeskClick = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setIsDeskDetailsModalOpen(true);
  };

  const handleDayClick = (date: Date) => {
    // Prevent clicking on disabled dates (past dates or dates too far in future)
    if (isDateDisabled(date)) {
      const today = startOfDay(new Date());
      if (isBefore(date, today)) {
        toast.error('Cannot book desks for past dates');
      } else {
        toast.error('Cannot book desks more than 90 days in advance');
      }
      return;
    }
    setSelectedDay(date);
    setIsDayBookingsModalOpen(true);
  };

  const handleBookFromCalendar = () => {
    setIsDayBookingsModalOpen(false);
    setIsAvailableDesksModalOpen(true);
  };

  const handleSelectDeskForBooking = (desk: DeskWithZone) => {
    setSelectedDesk(desk);
    setIsAvailableDesksModalOpen(false);
    
    // Set the selected day as both start and end date when booking from calendar
    if (selectedDay) {
      setStartDate(selectedDay);
      setEndDate(selectedDay);
    }
    
    setIsBookingDialogOpen(true);
  };

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>Book a Desk</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold tracking-tight">
              {viewMode === 'floor' ? 'Floor Plan' : 'Calendar View'}
            </h1>
            <p className="text-sm md:text-base text-muted-foreground">
              {viewMode === 'floor' 
                ? 'Interactive desk booking with flexible time slots'
                : 'Monthly overview of desk bookings and availability'
              }
            </p>
          </div>
          <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'floor' | 'calendar')}>
              <TabsList className="w-full sm:w-auto">
                <TabsTrigger value="floor" className="flex items-center gap-2 flex-1 sm:flex-initial">
                  <Grid className="h-4 w-4" />
                  <span className="hidden xs:inline">Floor Plan</span>
                  <span className="xs:hidden">Floor</span>
                </TabsTrigger>
                <TabsTrigger value="calendar" className="flex items-center gap-2 flex-1 sm:flex-initial">
                  <CalendarIcon className="h-4 w-4" />
                  <span className="hidden xs:inline">Calendar</span>
                  <span className="xs:hidden">Cal</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
            {viewMode === 'floor' && (
              <div className="grid grid-cols-2 gap-2 text-xs md:text-sm lg:flex lg:items-center lg:gap-4">
                <div className="flex items-center gap-1 md:gap-2">
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-green-500 rounded-full"></div>
                  <span className="truncate">Available</span>
                </div>
                <div className="flex items-center gap-1 md:gap-2">
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-blue-500 rounded-full"></div>
                  <span className="truncate">Soon Booked</span>
                </div>
                <div className="flex items-center gap-1 md:gap-2">
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-red-500 rounded-full"></div>
                  <span className="truncate">Occupied</span>
                </div>
                <div className="flex items-center gap-1 md:gap-2">
                  <div className="w-2 h-2 md:w-3 md:h-3 bg-yellow-500 rounded-full"></div>
                  <span className="truncate">Maintenance</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* User's Current Bookings */}
        {nearestBookings && nearestBookings.length > 0 && (
          <Card className="shadow-clay-sm border-blue-200">
            <CardHeader className="pb-3 md:pb-4">
              <CardTitle className="flex items-center gap-2 text-blue-700 text-lg md:text-xl">
                <User className="h-4 w-4 md:h-5 md:w-5" />
                Next 3 Bookings from {format(selectedDate, 'MMM dd, yyyy')}
              </CardTitle>
            </CardHeader>
            <CardContent className="px-3 md:px-6">
              <div className="space-y-3">
                {nearestBookings.map((booking) => (
                  <Card key={booking.id} className="shadow-clay-sm hover:shadow-clay-md transition-shadow">
                    <CardContent className="p-3 md:p-4">
                      <div className="flex flex-col gap-3 lg:flex-row lg:items-center lg:justify-between">
                        <div className="space-y-2 flex-1">
                          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
                            <h3 className="font-semibold text-base md:text-lg">{booking.desks?.[0]?.name}</h3>
                            <Badge className={getStatusColor(booking.status)}>
                              <div className="flex items-center gap-1">
                                {getStatusIcon(booking.status)}
                                <span className="text-xs">{booking.status}</span>
                              </div>
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 gap-2 text-xs md:text-sm">
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <MapPin className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0" />
                              <span className="truncate">{booking.desks?.[0]?.zones?.[0]?.name} • Floor {booking.desks?.[0]?.zones?.[0]?.floor_number}</span>
                            </div>
                            
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <CalendarIcon className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0" />
                              <span className="truncate">{formatDateRange(booking.date, booking.end_date)}</span>
                            </div>
                            
                            <div className="flex items-center gap-2 text-muted-foreground">
                              <Timer className="h-3 w-3 md:h-4 md:w-4 flex-shrink-0" />
                              <div className="flex flex-col sm:flex-row sm:items-center sm:gap-2">
                                <span>{formatTime(booking.start_time)} - {formatTime(booking.end_time)}</span>
                                <span className="text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded w-fit">
                                  {getBookingDuration(booking.start_time, booking.end_time, booking.date, booking.end_date)}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex gap-2 pt-2 lg:pt-0 lg:ml-4 lg:flex-col lg:gap-2">
                          <Button 
                            size="sm" 
                            variant="outline"
                            className="flex-1 lg:flex-initial text-xs"
                            onClick={() => {
                              const icsContent = generateICSFile({
                                deskName: booking.desks?.[0]?.name || 'Unknown Desk',
                                zoneName: booking.desks?.[0]?.zones?.[0]?.name || 'Unknown Zone',
                                startDate: booking.date,
                                endDate: booking.end_date,
                                startTime: booking.start_time,
                                endTime: booking.end_time,
                                userName: appUser?.name || 'You'
                              });
                              
                              const filename = `desk-booking-${booking.date}.ics`;
                              downloadICSFile(icsContent, filename);
                              toast.success('Calendar event downloaded!');
                            }}
                          >
                            <Download className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                            <span className="hidden sm:inline">Calendar</span>
                            <span className="sm:hidden">Calendar</span>
                          </Button>
                          
                          {canCheckIn(booking) && (
                            <Button 
                              size="sm"
                              className="flex-1 lg:flex-initial text-xs"
                              onClick={() => {
                                // Add check-in mutation
                                const checkInMutation = async () => {
                                  try {
                                    const { error } = await supabase
                                      .from('bookings')
                                      .update({ status: 'checked-in' })
                                      .eq('id', booking.id);
                                    
                                    if (error) throw error;
                                    
                                    queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
                                    queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
                                    queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
                                    toast.success('Successfully checked in!');
                                  } catch (error: any) {
                                    toast.error('Failed to check in: ' + error.message);
                                  }
                                };
                                checkInMutation();
                              }}
                            >
                              <LogIn className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                              <span className="hidden sm:inline">Check In</span>
                              <span className="sm:hidden">Check In</span>
                            </Button>
                          )}
                          
                          {canCancel(booking) && (
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button size="sm" variant="outline" className="flex-1 lg:flex-initial text-xs">
                                  <X className="h-3 w-3 md:h-4 md:w-4 mr-1" />
                                  <span className="hidden sm:inline">Cancel</span>
                                  <span className="sm:hidden">Cancel</span>
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent className="w-[95vw] max-w-lg mx-auto">
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Cancel Booking</AlertDialogTitle>
                                  <AlertDialogDescription className="text-sm">
                                    Are you sure you want to cancel your booking for {booking.desks?.[0]?.name} from {formatTime(booking.start_time)} to {formatTime(booking.end_time)} on {formatDateRange(booking.date, booking.end_date)}? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter className="flex-col gap-2 sm:flex-row">
                                  <AlertDialogCancel className="w-full sm:w-auto">Keep Booking</AlertDialogCancel>
                                  <AlertDialogAction 
                                    onClick={() => deleteBookingMutation.mutate(booking.id)}
                                    className="bg-red-600 hover:bg-red-700 w-full sm:w-auto"
                                    disabled={deleteBookingMutation.isPending}
                                  >
                                    Cancel Booking
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {viewMode === 'floor' ? (
          <FloorPlanView
            isLoading={isLoading}
            groupedData={groupedData}
            availableFloors={availableFloors}
            selectedFloor={selectedFloor}
            setSelectedFloor={setSelectedFloor}
            selectedDate={selectedDate}
            setSelectedDate={handleSelectedDateChange}
            isDateCalendarOpen={isDateCalendarOpen}
            setIsDateCalendarOpen={setIsDateCalendarOpen}
            getStatusForDate={getStatusForDate}
            getBookingForDate={getBookingForDate}
            getBookingCountForDate={getBookingCountForDay}
            getTotalDeskCount={getTotalDeskCount}
            isDateDisabled={isDateDisabled}
            handleDeskClick={handleDeskClick}
          />
        ) : (
          <CalendarView
            currentMonth={currentMonth}
            setCurrentMonth={setCurrentMonth}
            monthlyBookings={monthlyBookings}
            getBookingCountForDay={getBookingCountForDay}
            getAvailableCountForDay={getAvailableCountForDay}
            getTotalDeskCount={getTotalDeskCount}
            handleDayClick={handleDayClick}
            isDateDisabled={isDateDisabled}
          />
        )}

        {/* Enhanced Booking Dialog */}
        <BookingDialog
          isOpen={isBookingDialogOpen}
          onOpenChange={setIsBookingDialogOpen}
          selectedDesk={selectedDesk}
          startDate={startDate}
          endDate={endDate}
          startTime={startTime}
          endTime={endTime}
          isStartCalendarOpen={isStartCalendarOpen}
          isEndCalendarOpen={isEndCalendarOpen}
          onStartDateChange={setStartDate}
          onEndDateChange={setEndDate}
          onStartTimeChange={setStartTime}
          onEndTimeChange={setEndTime}
          onStartCalendarOpenChange={setIsStartCalendarOpen}
          onEndCalendarOpenChange={setIsEndCalendarOpen}
          isDateDisabled={isDateDisabled}
          hasTimeConflict={hasTimeConflict}
          hasUserBookingConflict={hasUserBookingConflict}
          isValidTimeRange={isValidTimeRange}
          isValidDateRange={isValidDateRange}
          canSubmitBooking={canSubmitBooking}
          onSubmit={handleBookDesk}
          isSubmitting={createBookingMutation.isPending}
          timeOptions={timeOptions}
        />

        {/* Modals */}
        <DeskDetailsModal
          isOpen={isDeskDetailsModalOpen}
          onOpenChange={setIsDeskDetailsModalOpen}
          selectedDesk={selectedDesk}
          selectedDate={selectedDate}
          getStatusForDate={getStatusForDate}
          getBookingForDate={getBookingForDate}
          getStatusBadgeColor={getStatusBadgeColor}
          getNextBookingsForDesk={getNextBookingsForDesk}
          formatTime={formatTime}
          formatDateRange={formatDateRange}
          onBookDesk={openBookingDialogFromModal}
        />

        <DayBookingsModal
          isOpen={isDayBookingsModalOpen}
          onOpenChange={setIsDayBookingsModalOpen}
          selectedDay={selectedDay}
          getBookingsForDay={getBookingsForDay}
          getAvailableCountForDay={getAvailableCountForDay}
          formatTime={formatTime}
          onBookAvailableDesk={handleBookFromCalendar}
        />

        <AvailableDesksModal
          isOpen={isAvailableDesksModalOpen}
          onOpenChange={setIsAvailableDesksModalOpen}
          selectedDay={selectedDay}
          getAvailableDesksForDay={getAvailableDesksForDay}
          onSelectDesk={handleSelectDeskForBooking}
        />
      </div>
    </>
  );
} 