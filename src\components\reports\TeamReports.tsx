import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { BarChart3, TrendingUp, Users, Building2, Calendar, Download } from 'lucide-react';
import { format, subDays } from 'date-fns';
import {
  exportUtilizationData,
  exportDeskUsageData,
  exportDepartmentData,
  exportZoneData
} from '@/utils/exportUtils';
import { ReportsSummary } from '@/components/reports/ReportsSummary';

type DateRange = '7d' | '30d' | '90d' | '1y';

interface UtilizationData {
  date: string;
  utilization: number;
  totalBookings: number;
  totalDesks: number;
}

interface DeskUsageData {
  deskName: string;
  zoneName: string;
  floorNumber: number;
  bookingCount: number;
  utilizationRate: number;
}

interface DepartmentData {
  department: string;
  bookingCount: number;
  userCount: number;
  avgBookingsPerUser: number;
}

interface ZoneData {
  zoneName: string;
  floorNumber: number;
  totalDesks: number;
  bookingCount: number;
  utilizationRate: number;
}

interface TeamReportsProps {
  teamMemberIds: string[];
  teamName: string;
}

export function TeamReports({ teamMemberIds, teamName }: TeamReportsProps) {
  const [dateRange, setDateRange] = useState<DateRange>('30d');
  const [activeTab, setActiveTab] = useState('utilization');

  // Calculate date range
  const getDateRange = (range: DateRange) => {
    const end = new Date();
    let start: Date;
    
    switch (range) {
      case '7d':
        start = subDays(end, 7);
        break;
      case '30d':
        start = subDays(end, 30);
        break;
      case '90d':
        start = subDays(end, 90);
        break;
      case '1y':
        start = subDays(end, 365);
        break;
      default:
        start = subDays(end, 30);
    }
    
    return { start, end };
  };

  const { start: startDate, end: endDate } = getDateRange(dateRange);

  // Fetch utilization data filtered by team members
  const { data: utilizationData, isLoading: utilizationLoading } = useQuery({
    queryKey: ['team-utilization-data', dateRange, teamMemberIds],
    queryFn: async () => {
      if (teamMemberIds.length === 0) return [];

      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select('date, desk_id, status')
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .in('status', ['booked', 'checked-in'])
        .in('user_id', teamMemberIds);

      if (bookingsError) throw bookingsError;

      const { data: desks, error: desksError } = await supabase
        .from('desks')
        .select('id');

      if (desksError) throw desksError;

      const totalDesks = desks.length;
      
      // Group bookings by date
      const bookingsByDate = bookings.reduce((acc, booking) => {
        const date = booking.date;
        if (!acc[date]) {
          acc[date] = new Set();
        }
        acc[date].add(booking.desk_id);
        return acc;
      }, {} as Record<string, Set<string>>);

      // Generate daily utilization data
      const utilizationData: UtilizationData[] = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const uniqueDesksBooked = bookingsByDate[dateStr]?.size || 0;
        const utilization = totalDesks > 0 ? (uniqueDesksBooked / totalDesks) * 100 : 0;
        
        utilizationData.push({
          date: dateStr,
          utilization: Math.round(utilization * 100) / 100,
          totalBookings: bookings.filter(b => b.date === dateStr).length,
          totalDesks
        });
        
        currentDate.setDate(currentDate.getDate() + 1);
      }

      return utilizationData;
    },
    enabled: teamMemberIds.length > 0,
  });

  // Fetch most used desks (filtered by team)
  const { data: deskUsageData, isLoading: deskUsageLoading } = useQuery({
    queryKey: ['team-desk-usage-data', dateRange, teamMemberIds],
    queryFn: async () => {
      if (teamMemberIds.length === 0) return [];

      const { data, error } = await supabase
        .from('bookings')
        .select(`
          desk_id,
          desks!inner(name, zone_id, zones!inner(name, floor_number))
        `)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .in('status', ['booked', 'checked-in'])
        .in('user_id', teamMemberIds);

      if (error) throw error;

      // Count bookings per desk
      const deskBookings = data.reduce((acc, booking) => {
        const deskId = booking.desk_id;
        if (!acc[deskId]) {
          acc[deskId] = {
            count: 0,
            desk: booking.desks
          };
        }
        acc[deskId].count++;
        return acc;
      }, {} as Record<string, { count: number; desk: any }>);

      // Calculate total possible bookings (days in range)
      const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      const deskUsageData: DeskUsageData[] = Object.values(deskBookings)
        .map(({ count, desk }) => ({
          deskName: desk.name,
          zoneName: desk.zones.name,
          floorNumber: desk.zones.floor_number,
          bookingCount: count,
          utilizationRate: Math.round((count / totalDays) * 100 * 100) / 100
        }))
        .sort((a, b) => b.bookingCount - a.bookingCount)
        .slice(0, 20);

      return deskUsageData;
    },
    enabled: teamMemberIds.length > 0,
  });

  // Fetch department data (filtered by team)
  const { data: departmentData, isLoading: departmentLoading, error: departmentError } = useQuery({
    queryKey: ['team-department-data', dateRange, teamMemberIds],
    queryFn: async () => {
      if (teamMemberIds.length === 0) return [];

      console.log('🔍 Starting team department query...');
      console.log('Date range:', { startDate: startDate.toISOString().split('T')[0], endDate: endDate.toISOString().split('T')[0] });
      console.log('📅 Date range setting:', dateRange);
      console.log('👥 Team member IDs:', teamMemberIds);
      
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          user_id
        `)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .in('status', ['booked', 'checked-in'])
        .in('user_id', teamMemberIds);

      console.log('Raw query result:', { data, error });

      if (error) {
        console.error('❌ Team department query error:', error);
        throw error;
      }

      if (!data || data.length === 0) {
        console.log('📊 No team bookings found for date range');
        return [];
      }

      console.log('🔍 Total team bookings found:', data.length);

      // Get unique user IDs from bookings
      const userIds = [...new Set(data.map(booking => booking.user_id))];
      console.log('🔍 Found team user IDs:', userIds);

      // Get user information for these IDs
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, department')
        .in('id', userIds);

      if (usersError) {
        console.error('❌ Team users query error:', usersError);
        throw usersError;
      }

      console.log('🔍 Team users data:', usersData);

      // Create a map of user ID to department
      const userDepartmentMap = usersData.reduce((acc, user) => {
        acc[user.id] = user.department || 'Unassigned';
        return acc;
      }, {} as Record<string, string>);

      console.log('🔍 Team user department map:', userDepartmentMap);

      // Group by department
      const departmentStats = data.reduce((acc, booking) => {
        const department = userDepartmentMap[booking.user_id] || 'Unassigned';
        
        if (!acc[department]) {
          acc[department] = {
            bookingCount: 0,
            users: new Set()
          };
        }
        acc[department].bookingCount++;
        acc[department].users.add(booking.user_id);
        return acc;
      }, {} as Record<string, { bookingCount: number; users: Set<string> }>);

      console.log('📊 Team department stats:', departmentStats);

      const departmentData: DepartmentData[] = Object.entries(departmentStats)
        .map(([department, stats]) => ({
          department,
          bookingCount: stats.bookingCount,
          userCount: stats.users.size,
          avgBookingsPerUser: Math.round((stats.bookingCount / stats.users.size) * 100) / 100
        }))
        .sort((a, b) => b.bookingCount - a.bookingCount);

      console.log('✅ Final team department data:', departmentData);
      return departmentData;
    },
    retry: 1,
    retryDelay: 1000,
    enabled: teamMemberIds.length > 0,
  });

  // Debug: Log department data state when it changes
  useEffect(() => {
    console.log('🔍 Team Department Query State:', {
      loading: departmentLoading,
      error: departmentError,
      data: departmentData,
      dataLength: departmentData?.length || 0
    });
  }, [departmentData, departmentLoading, departmentError]);

  // Fetch zone utilization data (filtered by team)
  const { data: zoneData, isLoading: zoneLoading } = useQuery({
    queryKey: ['team-zone-data', dateRange, teamMemberIds],
    queryFn: async () => {
      if (teamMemberIds.length === 0) return [];

      const { data: bookings, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          desk_id,
          desks!inner(zone_id, zones!inner(name, floor_number))
        `)
        .gte('date', startDate.toISOString().split('T')[0])
        .lte('date', endDate.toISOString().split('T')[0])
        .in('status', ['booked', 'checked-in'])
        .in('user_id', teamMemberIds);

      if (bookingsError) throw bookingsError;

      const { data: zones, error: zonesError } = await supabase
        .from('zones')
        .select(`
          id,
          name,
          floor_number,
          desks(id)
        `);

      if (zonesError) throw zonesError;

      // Count bookings per zone
      const zoneBookings = bookings.reduce((acc, booking) => {
        const zoneId = (booking.desks as any).zones.id || (booking.desks as any).zone_id;
        const zoneName = (booking.desks as any).zones.name;
        const floorNumber = (booking.desks as any).zones.floor_number;
        
        if (!acc[zoneId]) {
          acc[zoneId] = {
            zoneName,
            floorNumber,
            bookingCount: 0
          };
        }
        acc[zoneId].bookingCount++;
        return acc;
      }, {} as Record<string, { zoneName: string; floorNumber: number; bookingCount: number }>);

      const zoneData: ZoneData[] = zones.map((zone) => {
        const bookingData = zoneBookings[zone.id] || { zoneName: zone.name, floorNumber: zone.floor_number, bookingCount: 0 };
        const totalDesks = (zone.desks as any[]).length;
        const utilizationRate = totalDesks > 0 ? Math.round((bookingData.bookingCount / totalDesks) * 100) : 0;

        return {
          zoneName: zone.name,
          floorNumber: zone.floor_number,
          totalDesks,
          bookingCount: bookingData.bookingCount,
          utilizationRate
        };
      }).sort((a, b) => b.utilizationRate - a.utilizationRate);

      return zoneData;
    },
    enabled: teamMemberIds.length > 0,
  });

  // Chart configuration
  const chartConfig = {
    utilization: {
      label: "Utilization %",
      color: "#8B5CF6", // Purple
    },
    bookings: {
      label: "Bookings",
      color: "#06B6D4", // Cyan
    },
    bookingCount: {
      label: "Booking Count",
      color: "#F59E0B", // Amber
    },
    utilizationRate: {
      label: "Utilization Rate",
      color: "#EF4444", // Red
    },
    deskUsage: {
      label: "Desk Usage",
      color: "#10B981", // Emerald
    },
    departmentBookings: {
      label: "Department Bookings",
      color: "#3B82F6", // Blue
    },
    zoneUtilization: {
      label: "Zone Utilization",
      color: "#F97316", // Orange
    },
  };

  const pieColors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFA07A', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9', '#F8C471', '#82E0AA'];

  // Calculate summary statistics
  const summaryStats = {
    totalBookings: utilizationData?.reduce((sum, d) => sum + d.totalBookings, 0) || 0,
    totalUsers: departmentData?.reduce((sum, d) => sum + d.userCount, 0) || 0,
    totalDesks: utilizationData?.[0]?.totalDesks || 0,
    averageUtilization: Math.round((utilizationData?.reduce((sum, d) => sum + d.utilization, 0) || 0) / (utilizationData?.length || 1)),
    peakUtilization: Math.max(...(utilizationData?.map(d => d.utilization) || [0])),
    mostActiveDay: utilizationData && utilizationData.length > 0
      ? format(new Date(utilizationData.reduce((max, d) => d.totalBookings > max.totalBookings ? d : max, utilizationData[0]).date), 'MMM dd, yyyy')
      : 'N/A',
    mostUsedDesk: deskUsageData?.[0]?.deskName || 'N/A',
    topDepartment: departmentData?.[0]?.department || 'N/A',
    bookingTrend: 'stable' as const,
    trendPercentage: 0
  };

  const handleExport = () => {
    switch (activeTab) {
      case 'utilization':
        if (utilizationData) {
          exportUtilizationData(utilizationData, dateRange);
        }
        break;
      case 'desks':
        if (deskUsageData) {
          exportDeskUsageData(deskUsageData, dateRange);
        }
        break;
      case 'departments':
        if (departmentData) {
          exportDepartmentData(departmentData, dateRange);
        }
        break;
      case 'zones':
        if (zoneData) {
          exportZoneData(zoneData, dateRange);
        }
        break;
    }
  };

  if (teamMemberIds.length === 0) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <Users className="h-16 w-16 mx-auto mb-4 opacity-50" />
        <h3 className="text-lg font-medium">No Team Members</h3>
        <p className="text-base">Add team members to view reports</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{teamName} Reports</h1>
          <p className="text-muted-foreground">
            Analytics and insights for your team's workspace usage
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={(value: DateRange) => setDateRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Summary Statistics */}
      <ReportsSummary
        stats={summaryStats}
        dateRange={dateRange}
        isLoading={utilizationLoading || deskUsageLoading || departmentLoading || zoneLoading}
      />

      <Tabs defaultValue="utilization" className="w-full" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="utilization" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Utilization
          </TabsTrigger>
          <TabsTrigger value="desks" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Desk Usage
          </TabsTrigger>
          <TabsTrigger value="departments" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Departments
          </TabsTrigger>
          <TabsTrigger value="zones" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Zones
          </TabsTrigger>
        </TabsList>

        <TabsContent value="utilization" className="space-y-6 mt-6">
          {/* Utilization Overview Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Utilization</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {utilizationLoading ? '...' : `${Math.round((utilizationData?.reduce((sum, d) => sum + d.utilization, 0) || 0) / (utilizationData?.length || 1))}%`}
                </div>
                <p className="text-xs text-muted-foreground">
                  Last {dateRange === '7d' ? '7 days' : dateRange === '30d' ? '30 days' : dateRange === '90d' ? '90 days' : 'year'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Peak Utilization</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {utilizationLoading ? '...' : `${Math.max(...(utilizationData?.map(d => d.utilization) || [0]))}%`}
                </div>
                <p className="text-xs text-muted-foreground">
                  Highest single day
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {utilizationLoading ? '...' : utilizationData?.reduce((sum, d) => sum + d.totalBookings, 0) || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  In selected period
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Days</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {utilizationLoading ? '...' : utilizationData?.filter(d => d.totalBookings > 0).length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Days with bookings
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Utilization Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Daily Utilization Trend</CardTitle>
              <CardDescription>
                Percentage of desks utilized each day by your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {utilizationLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <ChartContainer config={chartConfig} className="h-80">
                  <LineChart data={utilizationData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="date"
                      tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                    />
                    <YAxis />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')}
                    />
                    <Line
                      type="monotone"
                      dataKey="utilization"
                      stroke="#8B5CF6"
                      strokeWidth={2}
                      dot={{ r: 4 }}
                    />
                  </LineChart>
                </ChartContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="desks" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Most Used Desks by Team</CardTitle>
              <CardDescription>
                Top performing desks by your team's booking frequency
              </CardDescription>
            </CardHeader>
            <CardContent>
              {deskUsageLoading ? (
                <div className="h-40 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Rank</TableHead>
                      <TableHead>Desk Name</TableHead>
                      <TableHead>Zone</TableHead>
                      <TableHead>Floor</TableHead>
                      <TableHead>Team Bookings</TableHead>
                      <TableHead>Utilization Rate</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {deskUsageData?.map((desk, index) => (
                      <TableRow key={`${desk.deskName}-${desk.zoneName}`}>
                        <TableCell className="font-medium">#{index + 1}</TableCell>
                        <TableCell className="font-medium">{desk.deskName}</TableCell>
                        <TableCell>{desk.zoneName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">Floor {desk.floorNumber}</Badge>
                        </TableCell>
                        <TableCell>{desk.bookingCount}</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-muted rounded-full h-2">
                              <div
                                className="bg-primary h-2 rounded-full"
                                style={{ width: `${Math.min(desk.utilizationRate, 100)}%` }}
                              />
                            </div>
                            <span className="text-sm">{desk.utilizationRate}%</span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Desk Usage Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Top 10 Team Desk Usage</CardTitle>
              <CardDescription>
                Booking frequency comparison for your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {deskUsageLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <ChartContainer config={chartConfig} className="h-80">
                  <BarChart data={deskUsageData?.slice(0, 10)}>
                    <defs>
                      <linearGradient id="colorTeamBookings" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#F59E0B" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#F59E0B" stopOpacity={0.2}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="deskName"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar
                      dataKey="bookingCount"
                      fill="url(#colorTeamBookings)"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ChartContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="departments" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2">
            {/* Department Statistics Table */}
            <Card>
              <CardHeader>
                <CardTitle>Team Department Analytics</CardTitle>
                <CardDescription>
                  Booking activity by department within your team
                </CardDescription>
              </CardHeader>
              <CardContent>
                {departmentLoading ? (
                  <div className="h-40 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : departmentError ? (
                  <div className="h-40 flex items-center justify-center">
                    <div className="text-red-500 text-center">
                      <p>Error loading department data</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {departmentError.message}
                      </p>
                    </div>
                  </div>
                ) : !departmentData || departmentData.length === 0 ? (
                  <div className="h-40 flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <p>No department data available</p>
                      <p className="text-sm mt-2">
                        Check browser console for details
                      </p>
                    </div>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Department</TableHead>
                        <TableHead>Users</TableHead>
                        <TableHead>Bookings</TableHead>
                        <TableHead>Avg/User</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {departmentData?.map((dept) => (
                        <TableRow key={dept.department}>
                          <TableCell className="font-medium">
                            {dept.department}
                          </TableCell>
                          <TableCell>{dept.userCount}</TableCell>
                          <TableCell>{dept.bookingCount}</TableCell>
                          <TableCell>{dept.avgBookingsPerUser}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

            {/* Department Distribution Pie Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Team Department Distribution</CardTitle>
                <CardDescription>
                  Booking share by department within your team
                </CardDescription>
              </CardHeader>
              <CardContent>
                {departmentLoading ? (
                  <div className="h-80 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : departmentError ? (
                  <div className="h-80 flex items-center justify-center">
                    <div className="text-red-500 text-center">
                      <p>Error loading department data</p>
                      <p className="text-sm text-muted-foreground mt-2">
                        {departmentError.message}
                      </p>
                    </div>
                  </div>
                ) : !departmentData || departmentData.length === 0 ? (
                  <div className="h-80 flex items-center justify-center">
                    <div className="text-center text-muted-foreground">
                      <p>No department data available</p>
                      <p className="text-sm mt-2">
                        Check browser console for details
                      </p>
                    </div>
                  </div>
                ) : (
                  <ChartContainer config={chartConfig} className="h-80">
                    <PieChart>
                      <Pie
                        data={departmentData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ department, percent }) => `${department} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="bookingCount"
                      >
                        {departmentData?.map((_, index) => (
                          <Cell key={`cell-${index}`} fill={pieColors[index % pieColors.length]} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ChartContainer>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Department Booking Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Team Department Booking Activity</CardTitle>
              <CardDescription>
                Total bookings per department within your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {departmentLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : departmentError ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="text-red-500 text-center">
                    <p>Error loading department data</p>
                    <p className="text-sm text-muted-foreground mt-2">
                      {departmentError.message}
                    </p>
                  </div>
                </div>
              ) : !departmentData || departmentData.length === 0 ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <p>No department data available</p>
                    <p className="text-sm mt-2">
                      Check browser console for details
                    </p>
                  </div>
                </div>
              ) : (
                <ChartContainer config={chartConfig} className="h-80">
                  <BarChart 
                    data={departmentData}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 60,
                    }}
                  >
                    <defs>
                      {pieColors.map((color, index) => (
                        <linearGradient key={`gradient-${index}`} id={`teamDepartmentGradient-${index}`} x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor={color} stopOpacity={0.8}/>
                          <stop offset="95%" stopColor={color} stopOpacity={0.3}/>
                        </linearGradient>
                      ))}
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="department"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      tick={{ fontSize: 11 }}
                      interval={0}
                    />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar
                      dataKey="bookingCount"
                      radius={[4, 4, 0, 0]}
                    >
                      {departmentData?.map((_, index) => (
                        <Cell key={`cell-${index}`} fill={`url(#teamDepartmentGradient-${index % pieColors.length})`} />
                      ))}
                    </Bar>
                  </BarChart>
                </ChartContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="zones" className="space-y-6 mt-6">
          {/* Zone Overview Cards */}
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Most Utilized Zone</CardTitle>
                <Building2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {zoneLoading ? '...' : zoneData?.[0]?.zoneName || 'N/A'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {zoneLoading ? '...' : `${zoneData?.[0]?.utilizationRate || 0}% utilization by team`}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Average Zone Utilization</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {zoneLoading ? '...' : `${Math.round((zoneData?.reduce((sum, z) => sum + z.utilizationRate, 0) || 0) / (zoneData?.length || 1))}%`}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across all zones by team
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Zone Bookings</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {zoneLoading ? '...' : zoneData?.reduce((sum, z) => sum + z.bookingCount, 0) || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  In selected period by team
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Zone Details Table */}
          <Card>
            <CardHeader>
              <CardTitle>Zone Performance Details</CardTitle>
              <CardDescription>
                Detailed breakdown of zone utilization by your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {zoneLoading ? (
                <div className="h-40 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Zone Name</TableHead>
                      <TableHead>Floor</TableHead>
                      <TableHead>Total Desks</TableHead>
                      <TableHead>Team Bookings</TableHead>
                      <TableHead>Team Utilization</TableHead>
                      <TableHead>Performance</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {zoneData?.map((zone) => (
                      <TableRow key={`${zone.zoneName}-${zone.floorNumber}`}>
                        <TableCell className="font-medium">{zone.zoneName}</TableCell>
                        <TableCell>
                          <Badge variant="outline">Floor {zone.floorNumber}</Badge>
                        </TableCell>
                        <TableCell>{zone.totalDesks}</TableCell>
                        <TableCell>{zone.bookingCount}</TableCell>
                        <TableCell>{zone.utilizationRate}%</TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="w-16 bg-muted rounded-full h-2">
                              <div
                                className="bg-primary h-2 rounded-full"
                                style={{ width: `${Math.min(zone.utilizationRate, 100)}%` }}
                              />
                            </div>
                            <Badge
                              variant={zone.utilizationRate > 70 ? "default" : zone.utilizationRate > 40 ? "secondary" : "outline"}
                            >
                              {zone.utilizationRate > 70 ? "High" : zone.utilizationRate > 40 ? "Medium" : "Low"}
                            </Badge>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Zone Comparison Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Zone Utilization Comparison</CardTitle>
              <CardDescription>
                Utilization rates across all zones by your team
              </CardDescription>
            </CardHeader>
            <CardContent>
              {zoneLoading ? (
                <div className="h-80 flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                </div>
              ) : (
                <ChartContainer config={chartConfig} className="h-80">
                  <BarChart data={zoneData}>
                    <defs>
                      <linearGradient id="colorTeamZones" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#F97316" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#F97316" stopOpacity={0.2}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="zoneName"
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar
                      dataKey="utilizationRate"
                      fill="url(#colorTeamZones)"
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ChartContainer>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
} 