-- Final fix for execute_query function with improved validation

-- Drop ALL versions of the function
DROP FUNCTION IF EXISTS execute_query(TEXT) CASCADE;

-- Create the definitive version with better validation
CREATE FUNCTION execute_query(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_result JSONB;
    cleaned_sql TEXT;
    first_word TEXT;
BEGIN
    -- Clean the SQL thoroughly
    cleaned_sql := query_sql;
    
    -- Remove any invisible characters
    cleaned_sql := REPLACE(cleaned_sql, chr(65279), ''); -- BOM
    cleaned_sql := REPLACE(cleaned_sql, chr(8203), ''); -- Zero-width space
    cleaned_sql := REPLACE(cleaned_sql, chr(160), ' '); -- Non-breaking space
    
    -- Normalize whitespace
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, '\s+', ' ', 'g');
    cleaned_sql := TRIM(cleaned_sql);
    
    -- Remove trailing semicolons
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, ';+\s*$', '');
    cleaned_sql := TRIM(cleaned_sql);
    
    -- Get the first word for validation (much more reliable)
    first_word := UPPER(SPLIT_PART(cleaned_sql, ' ', 1));
    
    -- Check if it starts with SELECT
    IF first_word != 'SELECT' THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed. First word: %', first_word;
    END IF;
    
    -- Check for prohibited operations using word boundaries (more precise)
    IF cleaned_sql ~* '\b(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE)\b' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for dangerous stored procedures
    IF cleaned_sql ~* '\b(EXEC|EXECUTE|xp_cmdshell|sp_executesql)\b' THEN
        RAISE EXCEPTION 'Query contains prohibited system functions';
    END IF;
    
    -- Check for SQL injection patterns
    IF cleaned_sql ~* ';\s*(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER)' THEN
        RAISE EXCEPTION 'Query contains dangerous SQL injection patterns';
    END IF;
    
    -- Add row limit if not present
    IF NOT (UPPER(cleaned_sql) LIKE '%LIMIT%') THEN
        cleaned_sql := cleaned_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', cleaned_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION execute_query(TEXT) TO authenticated;

-- Test the new function
SELECT 'Testing final execute_query version:' as test_info;

-- Test 1: Simple query
SELECT execute_query('SELECT 1 as test');

-- Test 2: The problematic query
SELECT execute_query('SELECT u.department, COUNT(b.id) AS booking_count FROM bookings b JOIN users u ON b.user_id = u.id GROUP BY u.department ORDER BY booking_count DESC');

-- Test 3: Verify function exists and is correct
SELECT 
    proname as function_name,
    prosecdef as security_definer,
    provolatile as volatility
FROM pg_proc 
WHERE proname = 'execute_query';

SELECT 'All tests completed successfully' as final_status; 