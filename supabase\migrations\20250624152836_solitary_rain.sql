/*
  # Create zones table

  1. New Tables
    - `zones`
      - `id` (uuid, primary key)
      - `name` (text, not null)
      - `floor_number` (integer, not null)
      - `description` (text, optional)
      - `created_at` (timestamptz with default)
      - `updated_at` (timestamptz with default)

  2. Security
    - Enable RLS on `zones` table
    - Add policy for authenticated users to read all zones
    - Add policy for admins to manage zones

  3. Triggers
    - Add trigger to automatically update `updated_at` timestamp
*/

-- Create zones table
CREATE TABLE IF NOT EXISTS zones (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL,
  floor_number integer NOT NULL,
  description text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE zones ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Authenticated users can read zones"
  ON zones
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage zones"
  ON zones
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- Create trigger for updated_at
CREATE TRIGGER update_zones_updated_at
  BEFORE UPDATE ON zones
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();