export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          role: 'employee' | 'manager' | 'admin';
          avatar?: string;
          phone?: string;
          bio?: string;
          location?: string;
          department?: string;
          manager_id?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          name: string;
          role?: 'employee' | 'manager' | 'admin';
          avatar?: string;
          phone?: string;
          bio?: string;
          location?: string;
          department?: string;
          manager_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          role?: 'employee' | 'manager' | 'admin';
          avatar?: string;
          phone?: string;
          bio?: string;
          location?: string;
          department?: string;
          manager_id?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      zones: {
        Row: {
          id: string;
          name: string;
          floor_number: number;
          description?: string;
          photo?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          floor_number: number;
          description?: string;
          photo?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          floor_number?: number;
          description?: string;
          photo?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      desks: {
        Row: {
          id: string;
          name: string;
          status: 'available' | 'occupied' | 'maintenance';
          zone_id: string;
          coordinates: { x: number; y: number };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          status?: 'available' | 'occupied' | 'maintenance';
          zone_id: string;
          coordinates: { x: number; y: number };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          status?: 'available' | 'occupied' | 'maintenance';
          zone_id?: string;
          coordinates?: { x: number; y: number };
          created_at?: string;
          updated_at?: string;
        };
      };
      bookings: {
        Row: {
          id: string;
          user_id: string;
          desk_id: string;
          date: string;
          end_date: string;
          start_time: string;
          end_time: string;
          status: 'booked' | 'checked-in' | 'cancelled';
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          desk_id: string;
          date: string;
          end_date: string;
          start_time: string;
          end_time: string;
          status?: 'booked' | 'checked-in' | 'cancelled';
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          desk_id?: string;
          date?: string;
          end_date?: string;
          start_time?: string;
          end_time?: string;
          status?: 'booked' | 'checked-in' | 'cancelled';
          created_at?: string;
          updated_at?: string;
        };
      };
      settings: {
        Row: {
          id: string;
          company_policies: Record<string, any>;
          working_hours: { start: string; end: string };
          booking_restrictions: Record<string, any>;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          company_policies?: Record<string, any>;
          working_hours?: { start: string; end: string };
          booking_restrictions?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          company_policies?: Record<string, any>;
          working_hours?: { start: string; end: string };
          booking_restrictions?: Record<string, any>;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}