import { format } from 'date-fns';

export function generateICSFile(booking: {
  deskName: string;
  zoneName: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  userName: string;
}) {
  const formatDateForICS = (date: string, time: string) => {
    const dateTime = new Date(`${date}T${time}`);
    return format(dateTime, "yyyyMMdd'T'HHmmss");
  };

  const startDateTime = formatDateForICS(booking.startDate, booking.startTime);
  const endDateTime = formatDateForICS(booking.endDate, booking.endTime);
  const created = format(new Date(), "yyyyMMdd'T'HHmmss");

  const icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//SymDesk//Desk Booking//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    `UID:${Date.now()}@symdesk.com`,
    `DTSTAMP:${created}`,
    `DTSTART:${startDateTime}`,
    `DTEND:${endDateTime}`,
    `SUMMARY:Desk Booking - ${booking.deskName}`,
    `DESCRIPTION:Desk: ${booking.deskName}\\nZone: ${booking.zoneName}\\nBooked by: ${booking.userName}`,
    `LOCATION:${booking.zoneName}`,
    'STATUS:CONFIRMED',
    'SEQUENCE:0',
    'END:VEVENT',
    'END:VCALENDAR'
  ].join('\r\n');

  return icsContent;
}

export function downloadICSFile(icsContent: string, filename: string) {
  const blob = new Blob([icsContent], { type: 'text/calendar;charset=utf-8' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
} 