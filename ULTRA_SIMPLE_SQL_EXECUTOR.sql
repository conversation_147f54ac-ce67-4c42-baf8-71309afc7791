-- Ultra simple SQL executor - NO validation, just execution
-- This function just runs whatever SQL you give it (SELECT only by parameter name)

DROP FUNCTION IF EXISTS execute_raw_sql(TEXT) CASCADE;
DROP FUNCTION IF EXISTS exec_sql(TEXT) CASCADE;
DROP FUNCTION IF EXISTS execute_query(TEXT) CASCADE;

-- Create the simplest possible function
CREATE OR REPLACE FUNCTION execute_raw_sql(sql_query TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    -- Execute the SQL directly - no validation at all
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', sql_query) INTO result;
    
    RETURN COALESCE(result, '[]'::JSON);
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION execute_raw_sql(TEXT) TO authenticated;

-- Test it with the exact SQL that was failing
SELECT 'Testing ultra simple executor:' as test_info;

SELECT execute_raw_sql('SELECT ''Development'' as department, 21 as total_bookings UNION SELECT ''LAB Team'', 11 ORDER BY total_bookings DESC');

-- Test with the actual problematic query (without semicolon)
SELECT execute_raw_sql('SELECT u.department, COUNT(b.id) AS total_bookings FROM bookings b JOIN users u ON b.user_id = u.id WHERE u.department IS NOT NULL GROUP BY u.department ORDER BY total_bookings DESC'); 