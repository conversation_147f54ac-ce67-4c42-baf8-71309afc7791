-- Alternative approach: More restrictive policy using a security definer function
-- Apply this script manually in your Supabase dashboard

-- This script fixes the issue where admin users cannot see all users in the ManageUsers page
-- It uses a security definer function to safely check admin roles without recursion

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can read own data" ON users;
DROP POLICY IF EXISTS "Admin users can read all data" ON users;
DROP POLICY IF EXISTS "Admin users can select all users" ON users;
DROP POLICY IF EXISTS "Users select policy" ON users;

-- Create a security definer function to check if current user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role FROM users WHERE id = auth.uid();
  RETURN user_role = 'admin';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a comprehensive SELECT policy using the security definer function
CREATE POLICY "Users can read based on role"
  ON users
  FOR SELECT
  TO authenticated
  USING (
    -- Users can always see their own data
    auth.uid() = id
    OR
    -- Admin users can see all data (using security definer function)
    is_admin()
  );

-- Function to update user metadata when role changes
-- This ensures that when a user is promoted to admin, their JWT includes the admin role
CREATE OR REPLACE FUNCTION update_user_metadata_on_role_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only update metadata if role has changed
  IF OLD.role IS DISTINCT FROM NEW.role THEN
    -- Update the auth.users metadata to include the new role
    UPDATE auth.users 
    SET raw_user_meta_data = COALESCE(raw_user_meta_data, '{}'::jsonb) || jsonb_build_object('role', NEW.role)
    WHERE id = NEW.id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically update user metadata when role changes
DROP TRIGGER IF EXISTS update_auth_user_metadata ON users;
CREATE TRIGGER update_auth_user_metadata
  AFTER UPDATE OF role ON users
  FOR EACH ROW 
  EXECUTE FUNCTION update_user_metadata_on_role_change();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA auth TO postgres;
GRANT UPDATE ON auth.users TO postgres; 