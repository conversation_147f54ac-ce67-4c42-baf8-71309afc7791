import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Loader2, Bot, Sparkles, Database, TrendingUp, Users, Building2, Calendar } from 'lucide-react';
import { toast } from 'sonner';

interface QueryResult {
  query: string;
  sql: string;
  results: any[];
  visualization: 'table' | 'chart' | 'metric';
  explanation: string;
  loading?: boolean;
  error?: string;
}

interface NaturalLanguageQueryProps {
  onQuery: (query: string) => Promise<QueryResult>;
  onResult?: (result: QueryResult | null) => void;
  isLoading?: boolean;
}

const EXAMPLE_QUERIES = [
  {
    text: "Who has the most bookings this week?",
    icon: TrendingUp,
    category: "Popular"
  },
  {
    text: "Which desks are most popular on Fridays?",
    icon: Building2,
    category: "Usage"
  },
  {
    text: "What's the average booking duration by department?",
    icon: Users,
    category: "Analytics"
  },
  {
    text: "Show me peak booking hours last month",
    icon: Calendar,
    category: "Trends"
  },
  {
    text: "Which floor has the highest utilization rate?",
    icon: Database,
    category: "Performance"
  },
  {
    text: "How many people worked late (after 6 PM) this week?",
    icon: Sparkles,
    category: "Insights"
  }
];

export function NaturalLanguageQuery({ onQuery, onResult, isLoading }: NaturalLanguageQueryProps) {
  const [query, setQuery] = useState('');
  const [result, setResult] = useState<QueryResult | null>(null);
  const [isQuerying, setIsQuerying] = useState(false);

  const handleQuery = async () => {
    if (!query.trim()) {
      toast.error('Please enter a question');
      return;
    }

    setIsQuerying(true);
    setResult(null);
    onResult?.(null); // Clear any previous results
    
    try {
      const queryResult = await onQuery(query);
      setResult(queryResult);
      onResult?.(queryResult); // Send result to parent
      
      if (queryResult.error) {
        toast.error(queryResult.error);
      } else {
        toast.success('Query executed successfully!');
      }
    } catch (error) {
      toast.error('Failed to execute query');
      console.error('Query error:', error);
    } finally {
      setIsQuerying(false);
    }
  };

  const handleExampleClick = (exampleQuery: string) => {
    setQuery(exampleQuery);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isQuerying) {
      handleQuery();
    }
  };

  return (
    <Card className="border-dashed border-primary/20 bg-gradient-to-r from-primary/5 to-transparent">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Bot className="h-5 w-5 text-primary" />
          Ask Your Data
          <Badge variant="secondary" className="ml-2">
            <Sparkles className="h-3 w-3 mr-1" />
            AI-Powered
          </Badge>
        </CardTitle>
        <CardDescription className="text-sm">
          Ask questions about your workspace data in plain English. Try asking about bookings, desks, departments, or usage patterns.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Query Input */}
        <div className="flex gap-2">
          <Input
            placeholder="e.g., Which person has the most bookings this week?"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
            disabled={isQuerying}
          />
          <Button 
            onClick={handleQuery} 
            disabled={isQuerying || !query.trim()}
            className="min-w-20"
          >
            {isQuerying ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <>
                <Bot className="h-4 w-4 mr-2" />
                Ask
              </>
            )}
          </Button>
        </div>

        {/* Example Queries */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <Sparkles className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm font-medium text-muted-foreground">Try these examples:</span>
          </div>
          
          <div className="grid gap-2 md:grid-cols-2">
            {EXAMPLE_QUERIES.map((example, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="justify-start h-auto p-3 text-left hover:bg-primary/5 hover:text-primary"
                onClick={() => handleExampleClick(example.text)}
                disabled={isQuerying}
              >
                <div className="flex items-start gap-2 w-full">
                  <example.icon className="h-4 w-4 mt-0.5 shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium">{example.text}</div>
                    <div className="text-xs text-muted-foreground">{example.category}</div>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {isQuerying && (
          <div className="flex items-center gap-2 p-4 bg-muted/50 rounded-lg">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">
              Analyzing your question and generating SQL query...
            </span>
          </div>
        )}

        {/* Query Result */}
        {result && (
          <div className="mt-4 space-y-3">
            {/* Generated SQL Display */}
            <div className="p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Database className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium text-muted-foreground">Generated SQL:</span>
              </div>
              <code className="text-xs bg-background px-2 py-1 rounded font-mono block overflow-x-auto">
                {result.sql}
              </code>
            </div>

            {/* AI Explanation */}
            <div className="p-3 bg-primary/5 rounded-lg border border-primary/20">
              <div className="flex items-center gap-2 mb-2">
                <Bot className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium text-primary">AI Explanation:</span>
              </div>
              <p className="text-sm text-muted-foreground">{result.explanation}</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 