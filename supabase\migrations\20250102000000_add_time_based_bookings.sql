/*
  # Add time-based booking support

  1. Updates
    - Add `start_time` (time) for booking start hour
    - Add `end_time` (time) for booking end hour  
    - Add `end_date` (date) for multi-day bookings
    - Update unique constraint to include time ranges
    - Add check constraint for valid time ranges

  2. Migration Strategy
    - Add new columns with defaults
    - Update existing data with default values
    - Create new constraint to prevent overlapping time bookings
*/

-- Add new time columns to bookings table
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS start_time time DEFAULT '09:00:00',
ADD COLUMN IF NOT EXISTS end_time time DEFAULT '17:00:00',
ADD COLUMN IF NOT EXISTS end_date date;

-- Update end_date to match date for existing records
UPDATE bookings SET end_date = date WHERE end_date IS NULL;

-- Make end_date NOT NULL
ALTER TABLE bookings ALTER COLUMN end_date SET NOT NULL;

-- Add check constraint for valid time ranges
ALTER TABLE bookings 
ADD CONSTRAINT check_valid_time_range 
CHECK (start_time < end_time);

-- Add check constraint for valid date ranges  
ALTER TABLE bookings
ADD CONSTRAINT check_valid_date_range
CHECK (date <= end_date);

-- Drop the old unique constraint
ALTER TABLE bookings DROP CONSTRAINT IF EXISTS bookings_desk_id_date_key;

-- Create function to check for overlapping bookings
CREATE OR REPLACE FUNCTION check_booking_overlap(
  p_desk_id uuid,
  p_start_date date,
  p_end_date date,
  p_start_time time,
  p_end_time time,
  p_booking_id uuid DEFAULT NULL
) RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM bookings 
    WHERE desk_id = p_desk_id
    AND status != 'cancelled'
    AND (p_booking_id IS NULL OR id != p_booking_id)
    AND (
      -- Date ranges overlap
      (p_start_date <= end_date AND p_end_date >= date)
      AND (
        -- If same date, check time overlap
        (p_start_date = date AND p_end_date = end_date AND p_start_time < end_time AND p_end_time > start_time)
        OR
        -- If multi-day booking
        (p_start_date < end_date OR p_end_date > date)
      )
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to prevent overlapping bookings
CREATE OR REPLACE FUNCTION prevent_booking_overlap() 
RETURNS TRIGGER AS $$
BEGIN
  IF check_booking_overlap(NEW.desk_id, NEW.date, NEW.end_date, NEW.start_time, NEW.end_time, NEW.id) THEN
    RAISE EXCEPTION 'Booking conflicts with existing reservation';
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to prevent overlapping bookings
DROP TRIGGER IF EXISTS booking_overlap_check ON bookings;
CREATE TRIGGER booking_overlap_check
  BEFORE INSERT OR UPDATE ON bookings
  FOR EACH ROW 
  WHEN (NEW.status != 'cancelled')
  EXECUTE FUNCTION prevent_booking_overlap();

-- Add indexes for time-based queries
CREATE INDEX IF NOT EXISTS idx_bookings_time_range ON bookings(desk_id, date, end_date, start_time, end_time);
CREATE INDEX IF NOT EXISTS idx_bookings_start_time ON bookings(start_time);
CREATE INDEX IF NOT EXISTS idx_bookings_end_time ON bookings(end_time);
CREATE INDEX IF NOT EXISTS idx_bookings_end_date ON bookings(end_date); 