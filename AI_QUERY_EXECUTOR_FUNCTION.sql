-- AI Query Executor Function
-- This function allows secure execution of AI-generated SQL queries
-- with proper validation and rate limiting

-- Create function to execute queries dynamically
CREATE OR REPLACE FUNCTION execute_query(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_result JSONB;
    row_count INTEGER;
BEGIN
    -- Validate that it's a SELECT query
    IF NOT (TRIM(UPPER(query_sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed';
    END IF;
    
    -- Check for prohibited operations
    IF query_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE|xp_|sp_)' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for SQL injection patterns
    IF query_sql ~* '(--|/\*|\*/|;\s*(DROP|DELETE|UPDATE|INSERT))' THEN
        RAISE EXCEPTION 'Query contains potentially dangerous patterns';
    END IF;
    
    -- Remove trailing semicolon if present
    query_sql := TRIM(query_sql);
    IF query_sql LIKE '%;' THEN
        query_sql := LEFT(query_sql, LENGTH(query_sql) - 1);
    END IF;
    
    -- Add row limit if not present (max 1000 rows)
    IF NOT (UPPER(query_sql) LIKE '%LIMIT%') THEN
        query_sql := query_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results as JSONB
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', query_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION execute_query(TEXT) TO authenticated;

-- Create a more secure version that respects RLS policies
CREATE OR REPLACE FUNCTION execute_query_with_rls(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY INVOKER -- Use caller's permissions
AS $$
DECLARE
    query_result JSONB;
    current_user_id UUID;
    current_user_role TEXT;
BEGIN
    -- Get current user info
    SELECT auth.uid() INTO current_user_id;
    SELECT role FROM users WHERE id = current_user_id INTO current_user_role;
    
    -- Validate that it's a SELECT query
    IF NOT (TRIM(UPPER(query_sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed';
    END IF;
    
    -- Check for prohibited operations
    IF query_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE|xp_|sp_)' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for SQL injection patterns
    IF query_sql ~* '(--|/\*|\*/|;\s*(DROP|DELETE|UPDATE|INSERT))' THEN
        RAISE EXCEPTION 'Query contains potentially dangerous patterns';
    END IF;
    
    -- For employees, ensure they can only see their own data
    IF current_user_role = 'employee' THEN
        -- Add user filter if not present
        IF NOT (query_sql ~* 'user_id\s*=|users\.id\s*=') THEN
            RAISE EXCEPTION 'Employee queries must include user restrictions';
        END IF;
    END IF;
    
    -- Remove trailing semicolon if present
    query_sql := TRIM(query_sql);
    IF query_sql LIKE '%;' THEN
        query_sql := LEFT(query_sql, LENGTH(query_sql) - 1);
    END IF;
    
    -- Add row limit if not present (max 1000 rows)
    IF NOT (UPPER(query_sql) LIKE '%LIMIT%') THEN
        query_sql := query_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results as JSONB
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', query_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION execute_query_with_rls(TEXT) TO authenticated;

-- Create rate limiting table for AI queries
CREATE TABLE IF NOT EXISTS ai_query_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    query_count INTEGER DEFAULT 0,
    last_reset_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create unique index on user_id for rate limiting
CREATE UNIQUE INDEX IF NOT EXISTS idx_ai_query_rate_limits_user_id ON ai_query_rate_limits(user_id);

-- Enable RLS on rate limits table
ALTER TABLE ai_query_rate_limits ENABLE ROW LEVEL SECURITY;

-- Drop existing policy if it exists and create new one
DROP POLICY IF EXISTS "Users can manage their own rate limits" ON ai_query_rate_limits;
CREATE POLICY "Users can manage their own rate limits" ON ai_query_rate_limits
    FOR ALL USING (auth.uid() = user_id);

-- Create function to check and update rate limits
CREATE OR REPLACE FUNCTION check_ai_query_rate_limit(max_queries INTEGER DEFAULT 100)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
DECLARE
    current_user_id UUID;
    current_count INTEGER;
    last_reset TIMESTAMP WITH TIME ZONE;
    hours_since_reset INTEGER;
BEGIN
    -- Get current user
    SELECT auth.uid() INTO current_user_id;
    
    -- Get current rate limit info
    SELECT query_count, last_reset_at 
    FROM ai_query_rate_limits 
    WHERE user_id = current_user_id
    INTO current_count, last_reset;
    
    -- If no record exists, create one
    IF current_count IS NULL THEN
        INSERT INTO ai_query_rate_limits (user_id, query_count, last_reset_at)
        VALUES (current_user_id, 1, NOW());
        RETURN TRUE;
    END IF;
    
    -- Check if 24 hours have passed since last reset
    SELECT EXTRACT(EPOCH FROM (NOW() - last_reset)) / 3600 INTO hours_since_reset;
    
    -- Reset counter if 24 hours have passed
    IF hours_since_reset >= 24 THEN
        UPDATE ai_query_rate_limits
        SET query_count = 1, last_reset_at = NOW()
        WHERE user_id = current_user_id;
        RETURN TRUE;
    END IF;
    
    -- Check if under limit
    IF current_count < max_queries THEN
        UPDATE ai_query_rate_limits
        SET query_count = query_count + 1
        WHERE user_id = current_user_id;
        RETURN TRUE;
    END IF;
    
    -- Rate limit exceeded
    RETURN FALSE;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION check_ai_query_rate_limit(INTEGER) TO authenticated;

-- Create audit table for AI queries
CREATE TABLE IF NOT EXISTS ai_query_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    natural_language_query TEXT NOT NULL,
    generated_sql TEXT NOT NULL,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    result_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on audit table
ALTER TABLE ai_query_audit ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and create new ones
DROP POLICY IF EXISTS "Users can view their own query history" ON ai_query_audit;
DROP POLICY IF EXISTS "Users can insert their own query records" ON ai_query_audit;
DROP POLICY IF EXISTS "Admins can view all query history" ON ai_query_audit;

-- Create RLS policies for audit table
CREATE POLICY "Users can view their own query history" ON ai_query_audit
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own query records" ON ai_query_audit
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Admins can view all query history
CREATE POLICY "Admins can view all query history" ON ai_query_audit
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Create function to log AI queries
CREATE OR REPLACE FUNCTION log_ai_query(
    nl_query TEXT,
    sql_query TEXT,
    exec_time INTEGER DEFAULT NULL,
    query_success BOOLEAN DEFAULT FALSE,
    error_msg TEXT DEFAULT NULL,
    result_cnt INTEGER DEFAULT 0
) RETURNS UUID
LANGUAGE plpgsql
AS $$
DECLARE
    audit_id UUID;
    current_user_id UUID;
BEGIN
    SELECT auth.uid() INTO current_user_id;
    
    INSERT INTO ai_query_audit (
        user_id, 
        natural_language_query, 
        generated_sql, 
        execution_time_ms, 
        success, 
        error_message, 
        result_count
    ) VALUES (
        current_user_id,
        nl_query,
        sql_query,
        exec_time,
        query_success,
        error_msg,
        result_cnt
    ) RETURNING id INTO audit_id;
    
    RETURN audit_id;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION log_ai_query(TEXT, TEXT, INTEGER, BOOLEAN, TEXT, INTEGER) TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ai_query_audit_user_id ON ai_query_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_query_audit_created_at ON ai_query_audit(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_query_audit_success ON ai_query_audit(success);

-- Create view for recent queries (OR REPLACE handles existing views)
CREATE OR REPLACE VIEW recent_ai_queries AS
SELECT 
    id,
    natural_language_query,
    success,
    result_count,
    execution_time_ms,
    created_at
FROM ai_query_audit
WHERE user_id = auth.uid()
ORDER BY created_at DESC
LIMIT 20;

-- Grant select permissions on the view
GRANT SELECT ON recent_ai_queries TO authenticated; 