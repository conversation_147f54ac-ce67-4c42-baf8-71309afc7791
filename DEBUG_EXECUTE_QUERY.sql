-- Debug version of execute_query to see what's happening with the SQL

CREATE OR REPLACE FUNCTION debug_execute_query(query_sql TEXT)
RETURNS TABLE(
    step TEXT,
    value TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    cleaned_sql TEXT;
    upper_sql TEXT;
    starts_with_select BOOLEAN;
BEGIN
    -- Return the original SQL
    RETURN QUERY SELECT 'original_sql'::TEXT, query_sql;
    
    -- Clean the SQL: remove trailing semicolon and whitespace
    cleaned_sql := TRIM(query_sql);
    RETURN QUERY SELECT 'after_trim'::TEXT, cleaned_sql;
    
    -- Remove semicolons
    WHILE cleaned_sql LIKE '%;' LOOP
        cleaned_sql := LEFT(cleaned_sql, LENGTH(cleaned_sql) - 1);
        cleaned_sql := TRIM(cleaned_sql);
    END LOOP;
    
    RETURN QUERY SELECT 'after_semicolon_removal'::TEXT, cleaned_sql;
    
    -- Check uppercase version
    upper_sql := TRIM(UPPER(cleaned_sql));
    RETURN QUERY SELECT 'upper_sql'::TEXT, upper_sql;
    
    -- Check if starts with SELECT
    starts_with_select := upper_sql LIKE 'SELECT%';
    RETURN QUERY SELECT 'starts_with_select'::TEXT, starts_with_select::TEXT;
    
    -- Show first 20 characters
    RETURN QUERY SELECT 'first_20_chars'::TEXT, LEFT(upper_sql, 20);
    
    -- Show length
    RETURN QUERY SELECT 'length'::TEXT, LENGTH(upper_sql)::TEXT;
    
    -- Show ASCII values of first few characters
    RETURN QUERY SELECT 'first_char_ascii'::TEXT, ASCII(LEFT(upper_sql, 1))::TEXT;
    
END;
$$;

-- Test with the problematic SQL
SELECT * FROM debug_execute_query('SELECT
    u.department,
    COUNT(b.id) AS total_bookings
FROM
    bookings b
JOIN
    users u ON b.user_id = u.id
WHERE
    u.department IS NOT NULL
GROUP BY
    u.department
ORDER BY
    total_bookings DESC;');

-- Test with simple SQL
SELECT * FROM debug_execute_query('SELECT 1 as test;'); 