import { supabase } from './supabase';
import { GeminiQueryService, QueryResult, GeminiQueryRequest } from './gemini-api';
import { QueryExecutor, queryExecutor } from './query-executor';

export interface AIQueryRequest {
  query: string;
  userId?: string;
  userRole?: 'employee' | 'manager' | 'admin';
}

export interface AIQueryResponse {
  success: boolean;
  result?: QueryResult;
  error?: string;
  executionTime?: number;
  rateLimitExceeded?: boolean;
  auditId?: string;
}

export class AIQueryService {
  private geminiService = new GeminiQueryService();

  async processQuery(request: AIQueryRequest): Promise<AIQueryResponse> {
    const startTime = Date.now();
    let auditId: string | undefined;
    let generatedSQL = '';

    try {
      // Check rate limiting
      const rateLimitOK = await this.checkRateLimit();
      if (!rateLimitOK) {
        return {
          success: false,
          error: 'Rate limit exceeded. Please try again later.',
          rateLimitExceeded: true,
          executionTime: Date.now() - startTime
        };
      }

      // Generate SQL using Gemini
      const geminiRequest: GeminiQueryRequest = {
        query: request.query,
        userId: request.userId,
        userRole: request.userRole || 'employee'
      };

      const geminiResult = await this.geminiService.processNaturalLanguageQuery(geminiRequest);
      
      // If Gemini returned an error, return it
      if (geminiResult.error) {
        auditId = await this.logQuery(request.query, '', Date.now() - startTime, false, geminiResult.error);
        return {
          success: false,
          error: geminiResult.error,
          executionTime: Date.now() - startTime,
          auditId
        };
      }

      generatedSQL = geminiResult.sql;

      // Execute the query with real data
      const executionResult = await queryExecutor.executeQuery(
        generatedSQL,
        request.userRole || 'employee',
        request.userId
      );

      const executionTime = Date.now() - startTime;

      if (!executionResult.success) {
        auditId = await this.logQuery(request.query, generatedSQL, executionTime, false, executionResult.error);
        return {
          success: false,
          error: executionResult.error,
          executionTime,
          auditId
        };
      }

      // Format the results
      const formattedResult = queryExecutor.formatResults(executionResult.data || [], request.query);
      formattedResult.sql = generatedSQL;

      // Log successful query
      auditId = await this.logQuery(
        request.query,
        generatedSQL,
        executionTime,
        true,
        undefined,
        executionResult.data?.length || 0
      );

      return {
        success: true,
        result: formattedResult,
        executionTime,
        auditId
      };

    } catch (error) {
      console.error('AI Query Service Error:', error);
      const executionTime = Date.now() - startTime;
      
      try {
        auditId = await this.logQuery(
          request.query,
          generatedSQL,
          executionTime,
          false,
          error instanceof Error ? error.message : 'Unknown error'
        );
      } catch (logError) {
        console.error('Failed to log query:', logError);
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        executionTime,
        auditId
      };
    }
  }

  private async checkRateLimit(): Promise<boolean> {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) return false;

      // Get current rate limit using normal Supabase query
      const { data: rateLimitData, error } = await supabase
        .from('ai_query_rate_limits')
        .select('query_count, last_reset_at')
        .eq('user_id', user.data.user.id)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Rate limit check failed:', error);
        return true; // Allow query if check fails
      }

      // If no record exists, create one and allow the query
      if (!rateLimitData) {
        await supabase
          .from('ai_query_rate_limits')
          .insert({
            user_id: user.data.user.id,
            query_count: 1,
            last_reset_at: new Date().toISOString()
          });
        return true;
      }

      // Check if 24 hours have passed since last reset
      const lastReset = new Date(rateLimitData.last_reset_at);
      const now = new Date();
      const hoursSinceReset = (now.getTime() - lastReset.getTime()) / (1000 * 60 * 60);

      // Reset if 24 hours have passed
      if (hoursSinceReset >= 24) {
        await supabase
          .from('ai_query_rate_limits')
          .update({
            query_count: 1,
            last_reset_at: now.toISOString()
          })
          .eq('user_id', user.data.user.id);
        return true;
      }

      // Check if under limit
      if (rateLimitData.query_count < 100) {
        await supabase
          .from('ai_query_rate_limits')
          .update({ query_count: rateLimitData.query_count + 1 })
          .eq('user_id', user.data.user.id);
        return true;
      }

      return false; // Rate limit exceeded
    } catch (error) {
      console.error('Rate limit check error:', error);
      return true; // Allow query if rate limit check fails
    }
  }

  private async logQuery(
    naturalLanguageQuery: string,
    generatedSQL: string,
    executionTime: number,
    success: boolean,
    errorMessage?: string,
    resultCount?: number
  ): Promise<string | undefined> {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) return undefined;

      // Insert audit log using normal Supabase query
      const { data, error } = await supabase
        .from('ai_query_audit')
        .insert({
          user_id: user.data.user.id,
          natural_language_query: naturalLanguageQuery,
          generated_sql: generatedSQL,
          execution_time_ms: executionTime,
          success: success,
          error_message: errorMessage || null,
          result_count: resultCount || 0
        })
        .select('id')
        .single();

      if (error) {
        console.error('Failed to log query:', error);
        return undefined;
      }

      return data?.id;
    } catch (error) {
      console.error('Query logging error:', error);
      return undefined;
    }
  }

  async getRecentQueries(): Promise<{
    success: boolean;
    queries?: Array<{
      id: string;
      natural_language_query: string;
      success: boolean;
      result_count: number;
      execution_time_ms: number;
      created_at: string;
    }>;
    error?: string;
  }> {
    try {
      const user = await supabase.auth.getUser();
      if (!user.data.user) {
        return {
          success: false,
          error: 'User not authenticated'
        };
      }

      // Query audit table directly using normal Supabase query
      const { data, error } = await supabase
        .from('ai_query_audit')
        .select('id, natural_language_query, success, result_count, execution_time_ms, created_at')
        .eq('user_id', user.data.user.id)
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) {
        return {
          success: false,
          error: error.message
        };
      }

      return {
        success: true,
        queries: data || []
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch recent queries'
      };
    }
  }

  async getUserRateLimit(): Promise<{
    success: boolean;
    remaining?: number;
    resetTime?: string;
    error?: string;
  }> {
    try {
      const { data, error } = await supabase
        .from('ai_query_rate_limits')
        .select('query_count, last_reset_at')
        .eq('user_id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      if (error && error.code !== 'PGRST116') { // Not found error
        return {
          success: false,
          error: error.message
        };
      }

      if (!data) {
        return {
          success: true,
          remaining: 100, // Default limit
          resetTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
        };
      }

      const lastReset = new Date(data.last_reset_at);
      const resetTime = new Date(lastReset.getTime() + 24 * 60 * 60 * 1000);
      const remaining = Math.max(0, 100 - data.query_count);

      return {
        success: true,
        remaining,
        resetTime: resetTime.toISOString()
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch rate limit'
      };
    }
  }
}

export const aiQueryService = new AIQueryService(); 