import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from 'next-themes';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { QueryProvider } from '@/contexts/QueryProvider';
import { Layout } from '@/components/layout/Layout';
import { AuthPage } from '@/components/auth/AuthPage';
import { Dashboard } from '@/pages/Dashboard';
import { FloorPlan } from '@/pages/FloorPlan';
import { Bookings } from '@/pages/Bookings';
import { Profile } from '@/pages/Profile';
import { DeskCheckIn } from '@/pages/DeskCheckIn';
import { AdminRoute } from '@/components/admin/AdminRoute';
import { AdminDashboard } from '@/pages/admin/AdminDashboard';
import { ManageUsers } from '@/pages/admin/ManageUsers';
import { ManageDesks } from '@/pages/admin/ManageDesks';
import { ManageZones } from '@/pages/admin/ManageZones';
import { Reports } from '@/pages/admin/Reports';
import AIQuery from '@/pages/admin/AIQuery';
import { ManagerRoute } from '@/components/manager/ManagerRoute';
import { ManagerDashboard } from '@/pages/manager/ManagerDashboard';
import { ManageTeam } from '@/pages/manager/ManageTeam';
import { PromoteToManager } from '@/components/temp/PromoteToManager';
import { Toaster } from '@/components/ui/sonner';
import './App.css';

function ProtectedRoute({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return user ? <>{children}</> : <Navigate to="/auth" replace />;
}

function AppRoutes() {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <Routes>
      <Route
        path="/auth"
        element={user ? <Navigate to="/" replace /> : <AuthPage />}
      />
      <Route
        path="/desk-checkin/:deskId"
        element={<DeskCheckIn />}
      />
      <Route
        path="/*"
        element={
          <ProtectedRoute>
            <Layout>
              <Routes>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/floor-plan" element={<FloorPlan />} />
                <Route path="/bookings" element={<Bookings />} />
                <Route path="/profile" element={<Profile />} />
                
                {/* Temporary Promotion Tool */}
                <Route path="/promote-manager" element={<PromoteToManager />} />
                
                {/* Manager Routes */}
                <Route path="/manager" element={
                  <ManagerRoute>
                    <ManagerDashboard />
                  </ManagerRoute>
                } />
                <Route path="/manager/team" element={
                  <ManagerRoute>
                    <ManageTeam />
                  </ManagerRoute>
                } />
                
                {/* Admin Routes */}
                <Route path="/admin" element={
                  <AdminRoute>
                    <AdminDashboard />
                  </AdminRoute>
                } />
                <Route path="/admin/users" element={
                  <AdminRoute>
                    <ManageUsers />
                  </AdminRoute>
                } />
                <Route path="/admin/zones" element={
                  <AdminRoute>
                    <ManageZones />
                  </AdminRoute>
                } />
                <Route path="/admin/desks" element={
                  <AdminRoute>
                    <ManageDesks />
                  </AdminRoute>
                } />
                <Route path="/admin/reports" element={
                  <AdminRoute>
                    <Reports />
                  </AdminRoute>
                } />
                <Route path="/admin/ai-query" element={
                  <AdminRoute>
                    <AIQuery />
                  </AdminRoute>
                } />
                <Route path="/admin/settings" element={
                  <AdminRoute>
                    <div className="p-6">
                      <h1 className="text-3xl font-bold">Settings</h1>
                      <p className="text-muted-foreground">Admin settings coming soon...</p>
                    </div>
                  </AdminRoute>
                } />
              </Routes>
            </Layout>
          </ProtectedRoute>
        }
      />
    </Routes>
  );
}

function App() {
  return (
    <QueryProvider>
      <AuthProvider>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <BrowserRouter>
            <AppRoutes />
            <Toaster />
          </BrowserRouter>
        </ThemeProvider>
      </AuthProvider>
    </QueryProvider>
  );
}

export default App;