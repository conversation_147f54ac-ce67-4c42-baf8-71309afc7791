-- ===================================
-- MANAGER ROLE & TEAM HIERARCHY SETUP
-- Run this in your Supabase SQL Editor
-- ===================================

-- Add manager role to users table role enum
ALTER TABLE users DROP CONSTRAINT IF EXISTS users_role_check;
ALTER TABLE users ADD CONSTRAINT users_role_check 
CHECK (role IN ('employee', 'manager', 'admin'));

-- Add manager_id field to create team hierarchy
ALTER TABLE users ADD COLUMN IF NOT EXISTS manager_id UUID REFERENCES users(id);

-- Add index for better performance on manager queries
CREATE INDEX IF NOT EXISTS idx_users_manager_id ON users(manager_id);

-- Update RLS policies to include manager access
-- Managers can see their team members
CREATE POLICY "Managers can view their team members" ON users
FOR SELECT USING (
  auth.uid() = id OR 
  auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
  auth.uid() = manager_id OR
  manager_id = auth.uid()
);

-- Managers can see their team's bookings
CREATE POLICY "Managers can view team bookings" ON bookings
FOR SELECT USING (
  user_id = auth.uid() OR
  auth.uid() IN (SELECT id FROM users WHERE role = 'admin') OR
  user_id IN (SELECT id FROM users WHERE manager_id = auth.uid())
);

-- Create a view for easy team member access
CREATE OR REPLACE VIEW user_teams AS
SELECT 
  m.id as manager_id,
  m.name as manager_name,
  m.email as manager_email,
  e.id as employee_id,
  e.name as employee_name,
  e.email as employee_email,
  e.role as employee_role,
  e.department as employee_department,
  e.avatar as employee_avatar
FROM users m
LEFT JOIN users e ON m.id = e.manager_id
WHERE m.role = 'manager';

-- Grant access to the view
GRANT SELECT ON user_teams TO authenticated;

-- Example: Update a user to be a manager (replace '<EMAIL>' with actual email)
-- UPDATE users SET role = 'manager' WHERE email = '<EMAIL>';

-- Example: Assign employees to a manager (replace IDs with actual values)
-- UPDATE users SET manager_id = 'manager-uuid-here' WHERE email IN ('<EMAIL>', '<EMAIL>');

-- Verify the setup
SELECT 
  'Total Users' as metric,
  COUNT(*) as count
FROM users
UNION ALL
SELECT 
  'Managers' as metric,
  COUNT(*) as count
FROM users WHERE role = 'manager'
UNION ALL
SELECT 
  'Employees with Managers' as metric,
  COUNT(*) as count
FROM users WHERE manager_id IS NOT NULL; 