/*
  # Create sample data for development

  1. Sample Data
    - Create sample zones (floors)
    - Create sample desks in each zone
    - Ensure proper relationships are established

  2. Purpose
    - Provide initial data for testing and development
    - Demonstrate the relationship structure
*/

-- Insert sample zones
INSERT INTO zones (id, name, floor_number, description) VALUES
  ('550e8400-e29b-41d4-a716-446655440001', 'Open Workspace', 1, 'Main open plan workspace area'),
  ('550e8400-e29b-41d4-a716-446655440002', 'Quiet Zone', 1, 'Quiet workspace for focused work'),
  ('550e8400-e29b-41d4-a716-446655440003', 'Collaboration Hub', 2, 'Collaborative workspace with meeting areas'),
  ('550e8400-e29b-41d4-a716-446655440004', 'Executive Floor', 3, 'Executive offices and meeting rooms')
ON CONFLICT (id) DO NOTHING;

-- Insert sample desks
INSERT INTO desks (name, status, zone_id, coordinates) VALUES
  -- Open Workspace (Floor 1)
  ('Desk 1A', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 100, "y": 100}'),
  ('Desk 1B', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 200, "y": 100}'),
  ('Desk 1C', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 300, "y": 100}'),
  ('Desk 1D', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 400, "y": 100}'),
  ('Desk 1E', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 100, "y": 200}'),
  ('Desk 1F', 'available', '550e8400-e29b-41d4-a716-446655440001', '{"x": 200, "y": 200}'),
  
  -- Quiet Zone (Floor 1)
  ('Quiet 1', 'available', '550e8400-e29b-41d4-a716-446655440002', '{"x": 500, "y": 100}'),
  ('Quiet 2', 'available', '550e8400-e29b-41d4-a716-446655440002', '{"x": 600, "y": 100}'),
  ('Quiet 3', 'available', '550e8400-e29b-41d4-a716-446655440002', '{"x": 500, "y": 200}'),
  ('Quiet 4', 'available', '550e8400-e29b-41d4-a716-446655440002', '{"x": 600, "y": 200}'),
  
  -- Collaboration Hub (Floor 2)
  ('Collab 2A', 'available', '550e8400-e29b-41d4-a716-446655440003', '{"x": 150, "y": 150}'),
  ('Collab 2B', 'available', '550e8400-e29b-41d4-a716-446655440003', '{"x": 250, "y": 150}'),
  ('Collab 2C', 'available', '550e8400-e29b-41d4-a716-446655440003', '{"x": 350, "y": 150}'),
  ('Collab 2D', 'available', '550e8400-e29b-41d4-a716-446655440003', '{"x": 450, "y": 150}'),
  
  -- Executive Floor (Floor 3)
  ('Exec 3A', 'available', '550e8400-e29b-41d4-a716-446655440004', '{"x": 200, "y": 200}'),
  ('Exec 3B', 'available', '550e8400-e29b-41d4-a716-446655440004', '{"x": 400, "y": 200}')
ON CONFLICT DO NOTHING;

/*
  # Add trigger to automatically create user records

  1. Function
    - Create function to handle new user creation
    - Extract user metadata and create user record

  2. Trigger
    - Add trigger on auth.users insert
    - Automatically create corresponding record in public.users table

  3. Policy Updates
    - Add policy for trigger function to insert users
*/

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'employee')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user record
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA auth TO postgres;
GRANT SELECT ON auth.users TO postgres;