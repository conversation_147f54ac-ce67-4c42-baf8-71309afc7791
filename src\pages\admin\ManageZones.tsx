import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Zone } from '@/types';
import { Building, Plus, Search, Edit, Trash2, MapPin } from 'lucide-react';

export function ManageZones() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedZone, setSelectedZone] = useState<Zone | null>(null);
  const [newZone, setNewZone] = useState({
    name: '',
    floor_number: 1,
    description: '',
    photo: null as File | null
  });
  const [editZone, setEditZone] = useState({
    name: '',
    floor_number: 1,
    description: '',
    photo: null as File | null
  });
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [editPhotoPreview, setEditPhotoPreview] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Fetch zones with desk count
  const { data: zones, isLoading } = useQuery({
    queryKey: ['admin-zones'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('zones')
        .select(`
          *,
          desks:desks(count)
        `)
        .order('floor_number', { ascending: true });
      
      if (error) throw error;
      return data as (Zone & { desks: { count: number }[] })[];
    },
  });

  // Helper function to upload zone photo
  const uploadZonePhoto = async (file: File, zoneName: string): Promise<string | null> => {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `zone-${zoneName.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('zone-photos')
        .upload(fileName, file, { 
          cacheControl: '3600',
          upsert: true 
        });

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('zone-photos')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      console.error('Photo upload error:', error);
      throw error;
    }
  };

  // Mutation to create new zone
  const createZone = useMutation({
    mutationFn: async (zone: typeof newZone) => {
      let photoUrl = null;
      
      // Upload photo if provided
      if (zone.photo) {
        photoUrl = await uploadZonePhoto(zone.photo, zone.name);
      }
      
      const { error } = await supabase
        .from('zones')
        .insert({
          name: zone.name,
          floor_number: zone.floor_number,
          description: zone.description || null,
          photo: photoUrl
        });
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-zones'] });
      queryClient.invalidateQueries({ queryKey: ['zones'] }); // Also invalidate the zones query used in desk management
      toast.success('Zone created successfully');
      setIsDialogOpen(false);
      setNewZone({ name: '', floor_number: 1, description: '', photo: null });
      setPhotoPreview(null);
    },
    onError: (error) => {
      toast.error('Failed to create zone: ' + error.message);
    },
  });

  // Mutation to update zone
  const updateZone = useMutation({
    mutationFn: async ({ zoneId, updates }: { zoneId: string; updates: typeof editZone }) => {
      let photoUrl = updates.photo ? await uploadZonePhoto(updates.photo, updates.name) : undefined;
      
      const updateData: any = {
        name: updates.name,
        floor_number: updates.floor_number,
        description: updates.description || null
      };
      
      if (photoUrl) {
        updateData.photo = photoUrl;
      }
      
      const { error } = await supabase
        .from('zones')
        .update(updateData)
        .eq('id', zoneId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-zones'] });
      queryClient.invalidateQueries({ queryKey: ['zones'] });
      toast.success('Zone updated successfully');
      setIsEditDialogOpen(false);
      setSelectedZone(null);
      setEditPhotoPreview(null);
    },
    onError: (error) => {
      toast.error('Failed to update zone: ' + error.message);
    },
  });

  // Mutation to delete zone
  const deleteZone = useMutation({
    mutationFn: async (zoneId: string) => {
      const { error } = await supabase
        .from('zones')
        .delete()
        .eq('id', zoneId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-zones'] });
      queryClient.invalidateQueries({ queryKey: ['zones'] });
      toast.success('Zone deleted successfully');
      setIsDeleteDialogOpen(false);
      setSelectedZone(null);
    },
    onError: (error) => {
      toast.error('Failed to delete zone: ' + error.message);
    },
  });

  // Filter zones based on search term
  const filteredZones = zones?.filter(zone =>
    zone.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    zone.floor_number.toString().includes(searchTerm)
  ) || [];

  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>, isEdit = false) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image must be less than 5MB');
        return;
      }

      if (isEdit) {
        setEditZone({ ...editZone, photo: file });
        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setEditPhotoPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      } else {
        setNewZone({ ...newZone, photo: file });
        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
          setPhotoPreview(e.target?.result as string);
        };
        reader.readAsDataURL(file);
      }
    }
  };

  const handleCreateZone = () => {
    if (!newZone.name || newZone.floor_number < 1) {
      toast.error('Please fill in all required fields');
      return;
    }
    createZone.mutate(newZone);
  };

  const handleEditZone = (zone: Zone) => {
    setSelectedZone(zone);
    setEditZone({
      name: zone.name,
      floor_number: zone.floor_number,
      description: zone.description || '',
      photo: null
    });
    setEditPhotoPreview(zone.photo || null);
    setIsEditDialogOpen(true);
  };

  const handleUpdateZone = () => {
    if (!editZone.name || editZone.floor_number < 1 || !selectedZone) {
      toast.error('Please fill in all required fields');
      return;
    }
    updateZone.mutate({
      zoneId: selectedZone.id,
      updates: editZone
    });
  };

  const handleDeleteClick = (zone: Zone & { desks: { count: number }[] }) => {
    setSelectedZone(zone);
    setIsDeleteDialogOpen(true);
  };

  const handleConfirmDelete = () => {
    if (!selectedZone) return;
    deleteZone.mutate(selectedZone.id);
  };

  const getDeskCount = (zone: Zone & { desks: { count: number }[] }) => {
    return zone.desks?.[0]?.count || 0;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Manage Zones</h1>
          <p className="text-muted-foreground">
            Configure workspace zones and floor layouts
          </p>
        </div>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Zone
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Zone</DialogTitle>
              <DialogDescription>
                Create a new zone in your workspace
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Zone Name
                </Label>
                <Input
                  id="name"
                  value={newZone.name}
                  onChange={(e) => setNewZone({ ...newZone, name: e.target.value })}
                  className="col-span-3"
                  placeholder="e.g., Open Workspace, Meeting Area"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="floor" className="text-right">
                  Floor Number
                </Label>
                <Input
                  id="floor"
                  type="number"
                  min="1"
                  value={newZone.floor_number}
                  onChange={(e) => setNewZone({ ...newZone, floor_number: parseInt(e.target.value) || 1 })}
                  className="col-span-3"
                  placeholder="e.g., 1, 2, 3"
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="description" className="text-right mt-2">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newZone.description}
                  onChange={(e) => setNewZone({ ...newZone, description: e.target.value })}
                  className="col-span-3"
                  placeholder="Optional description of the zone..."
                  rows={3}
                />
              </div>
              <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="photo" className="text-right mt-2">
                  Photo
                </Label>
                <div className="col-span-3 space-y-3">
                  {photoPreview && (
                    <div className="flex justify-center">
                      <img 
                        src={photoPreview} 
                        alt="Zone preview" 
                        className="w-32 h-32 object-cover rounded-lg border"
                      />
                    </div>
                  )}
                  <Input
                    id="photo"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handlePhotoChange(e, false)}
                    className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                  />
                  <p className="text-xs text-muted-foreground">
                    Optional. Maximum file size: 5MB. Supported formats: JPG, PNG, GIF
                  </p>
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={handleCreateZone} disabled={createZone.isPending}>
                {createZone.isPending ? 'Creating...' : 'Create Zone'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Zones</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{zones?.length || 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Floors</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {new Set(zones?.map(z => z.floor_number)).size || 0}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Desks</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {zones?.reduce((sum, zone) => sum + getDeskCount(zone), 0) || 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Zones Table */}
      <Card>
        <CardHeader>
          <CardTitle>Zones</CardTitle>
          <CardDescription>
            Manage workspace zones and their configurations
          </CardDescription>
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search zones..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Zone Name</TableHead>
                <TableHead>Floor</TableHead>
                <TableHead>Desks</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredZones.map((zone) => (
                <TableRow key={zone.id}>
                  <TableCell className="font-medium">{zone.name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">Floor {zone.floor_number}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">{getDeskCount(zone)} desks</Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(zone.created_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditZone(zone)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(zone)}
                        className="text-red-600 hover:text-red-700"
                        disabled={getDeskCount(zone) > 0}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredZones.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No zones found matching your search.
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Zone Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Zone</DialogTitle>
            <DialogDescription>
              Update zone information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Zone Name
              </Label>
              <Input
                id="edit-name"
                value={editZone.name}
                onChange={(e) => setEditZone({ ...editZone, name: e.target.value })}
                className="col-span-3"
                placeholder="e.g., Open Workspace"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-floor" className="text-right">
                Floor Number
              </Label>
              <Input
                id="edit-floor"
                type="number"
                min="1"
                value={editZone.floor_number}
                onChange={(e) => setEditZone({ ...editZone, floor_number: parseInt(e.target.value) || 1 })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="edit-description" className="text-right mt-2">
                Description
              </Label>
              <Textarea
                id="edit-description"
                value={editZone.description}
                onChange={(e) => setEditZone({ ...editZone, description: e.target.value })}
                className="col-span-3"
                placeholder="Optional description of the zone..."
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label htmlFor="edit-photo" className="text-right mt-2">
                Photo
              </Label>
              <div className="col-span-3 space-y-3">
                {editPhotoPreview && (
                  <div className="flex justify-center">
                    <img 
                      src={editPhotoPreview} 
                      alt="Zone preview" 
                      className="w-32 h-32 object-cover rounded-lg border"
                    />
                  </div>
                )}
                <Input
                  id="edit-photo"
                  type="file"
                  accept="image/*"
                  onChange={(e) => handlePhotoChange(e, true)}
                  className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                />
                <p className="text-xs text-muted-foreground">
                  Optional. Maximum file size: 5MB. Supported formats: JPG, PNG, GIF
                </p>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleUpdateZone} disabled={updateZone.isPending}>
              {updateZone.isPending ? 'Updating...' : 'Update Zone'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Zone</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedZone?.name}"? This action cannot be undone.
              {selectedZone && getDeskCount(selectedZone as any) > 0 && (
                <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-sm">
                  ❌ Cannot delete: This zone contains {getDeskCount(selectedZone as any)} desk(s). 
                  Please move or delete all desks from this zone first.
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleConfirmDelete} 
              disabled={deleteZone.isPending || !!(selectedZone && getDeskCount(selectedZone as any) > 0)}
            >
              {deleteZone.isPending ? 'Deleting...' : 'Delete Zone'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 