/*
  # Update handle_new_user trigger to support manager_id

  1. Problem
    - Current trigger doesn't handle manager_id from signup metadata
    - Need to extract manager_id from raw_user_meta_data

  2. Solution
    - Update handle_new_user function to extract manager_id
    - Store manager_id in users table when user is created

  3. Security
    - Function runs with SECURITY DEFINER to bypass RLS
    - Only extracts manager_id from trusted metadata source
*/

-- Update function to handle new user creation with manager_id
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, role, manager_id)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'employee'),
    CASE 
      WHEN NEW.raw_user_meta_data->>'manager_id' = '' THEN NULL
      WHEN NEW.raw_user_meta_data->>'manager_id' = 'none' THEN NULL
      WHEN NEW.raw_user_meta_data->>'manager_id' IS NULL THEN NULL
      ELSE NEW.raw_user_meta_data->>'manager_id'
    END
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 