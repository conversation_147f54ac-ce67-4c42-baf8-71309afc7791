import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Calendar, MapPin } from 'lucide-react';
import { format, startOfWeek, addDays, addWeeks, subWeeks, parseISO } from 'date-fns';

interface BookingData {
  id: string;
  date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  status: string;
  user_id: string;
  desk: {
    name: string;
    zone: {
      name: string;
      floor_number: number;
    };
  };
}

interface UserData {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
  department?: string;
}

interface BookingsCalendarViewProps {
  bookings: BookingData[];
  users: UserData[];
  currentUserId?: string; // For single user view
  title: string;
  showUserColumn?: boolean;
  isLoading?: boolean;
}

export function BookingsCalendarView({ 
  bookings = [], 
  users = [], 
  currentUserId,
  title,
  showUserColumn = true,
  isLoading = false
}: BookingsCalendarViewProps) {
  const [currentWeek, setCurrentWeek] = useState(new Date());

  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 }); // Start on Monday
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));

  const filteredUsers = currentUserId 
    ? users.filter(user => user.id === currentUserId)
    : users;

  const getBookingColor = (status: string) => {
    switch (status) {
      case 'booked':
        // Use the user's unique color
        return `hover:opacity-80`;
      case 'checked-in':
        // Slightly more saturated/brighter for checked-in
        return `hover:opacity-80`;
      case 'cancelled':
        // Desaturated version for cancelled
        return 'bg-red-300 hover:bg-red-400';
      default:
        return 'bg-gray-400 hover:bg-gray-500';
    }
  };

  const getBookingStyle = (status: string, userId: string) => {
    let hash = 0;
    for (let i = 0; i < userId.length; i++) {
      hash = ((hash << 5) - hash + userId.charCodeAt(i)) & 0xffffffff;
    }
    
    const hue = Math.abs(hash) % 360;
    
    switch (status) {
      case 'booked':
        return {
          backgroundColor: `hsl(${hue}, 65%, 55%)`,
        };
      case 'checked-in':
        return {
          backgroundColor: `hsl(${hue}, 75%, 65%)`, // Brighter for checked-in
        };
      case 'cancelled':
        return {
          backgroundColor: '#fca5a5', // Red for cancelled
        };
      default:
        return {
          backgroundColor: '#9ca3af', // Gray for default
        };
    }
  };

  const getBookingsForUserAndDate = (userId: string, date: Date) => {
    const result = bookings.filter(booking => {
      const bookingStart = parseISO(booking.date);
      const bookingEnd = parseISO(booking.end_date);
      
      const userMatches = booking.user_id === userId;
      const dateInRange = date >= bookingStart && date <= bookingEnd;
      const notCancelled = booking.status !== 'cancelled';
      
      // Check if the date falls within the booking range
      return userMatches && dateInRange && notCancelled;
    });
    
    return result;
  };

  const formatTime = (timeString: string) => {
    const time = new Date(`2000-01-01 ${timeString}`);
    return time.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const isMultiDayBooking = (booking: BookingData) => {
    return booking.date !== booking.end_date;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-4 w-4 md:h-5 md:w-5" />
            <span className="text-base md:text-lg">{title}</span>
          </CardTitle>
          <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentWeek(subWeeks(currentWeek, 1))}
              className="h-8 w-8 p-0 md:h-10 md:w-auto md:px-3"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only md:not-sr-only md:ml-1">Previous</span>
            </Button>
            <span className="font-medium text-xs md:text-sm px-2 md:px-4 text-center">
              {format(weekStart, 'MMM dd')} - {format(addDays(weekStart, 6), 'MMM dd, yyyy')}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentWeek(addWeeks(currentWeek, 1))}
              className="h-8 w-8 p-0 md:h-10 md:w-auto md:px-3"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only md:not-sr-only md:ml-1">Next</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Mobile: Show a simplified view with horizontal scroll */}
        <div className="block md:hidden">
          <div className="border rounded-lg overflow-x-auto">
            <div className="min-w-[600px]">
              {/* Mobile Header */}
              <div className={`bg-muted/50 border-b grid ${
                showUserColumn ? 'grid-cols-8' : 'grid-cols-7'
              }`}>
                {showUserColumn && (
                  <div className="p-2 font-medium border-r text-xs">
                    Team
                  </div>
                )}
                {weekDays.map((day) => (
                  <div
                    key={day.toISOString()}
                    className="p-2 text-center font-medium border-r last:border-r-0"
                  >
                    <div className="text-xs text-muted-foreground">
                      {format(day, 'EEE')}
                    </div>
                    <div className="text-sm font-semibold">
                      {format(day, 'dd')}
                    </div>
                  </div>
                ))}
              </div>

              {/* Mobile Body */}
              <div className="divide-y">
                {filteredUsers.length > 0 ? (
                  filteredUsers.map((user) => {
                    return (
                      <div key={user.id} className={`grid min-h-[60px] relative ${
                        showUserColumn ? 'grid-cols-8' : 'grid-cols-7'
                      }`}>
                        {showUserColumn && (
                          <div className="p-2 border-r flex items-center gap-2 bg-muted/20">
                            <Avatar className="h-6 w-6">
                              <AvatarImage src={user.avatar || ''} alt={user.name} />
                              <AvatarFallback className="text-xs">
                                {user.name?.charAt(0)?.toUpperCase() || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-xs truncate">{user.name}</div>
                            </div>
                          </div>
                        )}

                        {weekDays.map((day) => {
                          const dayBookings = getBookingsForUserAndDate(user.id, day);
                          const singleDayBookings = dayBookings.filter(booking => !isMultiDayBooking(booking));

                          return (
                            <div
                              key={`${user.id}-${day.toISOString()}`}
                              className="p-1 border-r last:border-r-0 relative min-h-[60px] flex flex-col justify-center"
                            >
                              {singleDayBookings.map((booking) => (
                                <div
                                  key={`${booking.id}-mobile`}
                                  className={`${getBookingColor(booking.status)} text-white text-xs p-1 rounded mb-1 cursor-pointer transition-colors relative`}
                                  style={getBookingStyle(booking.status, user.id)}
                                  title={`${booking.desk.name} (${booking.desk.zone.name}) - ${formatTime(booking.start_time)} to ${formatTime(booking.end_time)}`}
                                >
                                  <div className="flex items-center gap-1 truncate">
                                    <MapPin className="h-2 w-2 flex-shrink-0" />
                                    <span className="truncate font-medium text-xs">
                                      {booking.desk.name}
                                    </span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          );
                        })}
                      </div>
                    );
                  })
                ) : (
                  <div className="p-8 text-center text-muted-foreground">
                    <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No team members or bookings to display</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Desktop: Show responsive grid */}
        <div className="hidden md:block border rounded-lg overflow-hidden">
          {/* Header */}
          <div className={`bg-muted/50 border-b grid ${
            showUserColumn
              ? 'grid-cols-8 lg:grid-cols-8 md:grid-cols-4 sm:grid-cols-2'
              : 'grid-cols-7 lg:grid-cols-7 md:grid-cols-4 sm:grid-cols-2'
          }`}>
            {showUserColumn && (
              <div className="p-2 md:p-3 font-medium border-r text-xs md:text-sm">
                <span className="hidden sm:inline">Team Members</span>
                <span className="sm:hidden">Team</span>
              </div>
            )}
            {weekDays.map((day, index) => (
              <div
                key={day.toISOString()}
                className={`p-2 md:p-3 text-center font-medium border-r last:border-r-0 ${
                  showUserColumn ? '' : index === 0 ? 'col-span-1' : ''
                } ${
                  // Hide some days on smaller screens
                  index >= 2 && index < 5 ? 'hidden md:block' : ''
                } ${
                  index >= 4 ? 'hidden lg:block' : ''
                }`}
              >
                <div className="text-xs md:text-sm text-muted-foreground">
                  {format(day, 'EEE')}
                </div>
                <div className="text-sm md:text-lg font-semibold">
                  {format(day, 'dd')}
                </div>
              </div>
            ))}
          </div>

          {/* Body */}
          <div className="divide-y">
            {filteredUsers.length > 0 ? (
              filteredUsers.map((user) => {
                const userBookings = bookings.filter(b => b.user_id === user.id);

                // Get multi-day bookings for this user in this week
                const multiDayBookings = userBookings.filter(booking => {
                  const isMultiDay = isMultiDayBooking(booking);
                  if (!isMultiDay) return false;

                  const bookingStart = parseISO(booking.date);
                  const bookingEnd = parseISO(booking.end_date);
                  const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 });

                  // Check if booking overlaps with this week
                  return bookingStart <= addDays(weekStart, 6) && bookingEnd >= weekStart;
                });

                return (
                  <div key={user.id} className={`grid min-h-[60px] md:min-h-[80px] relative ${
                    showUserColumn
                      ? 'grid-cols-8 lg:grid-cols-8 md:grid-cols-4 sm:grid-cols-2'
                      : 'grid-cols-7 lg:grid-cols-7 md:grid-cols-4 sm:grid-cols-2'
                  }`}>
                    {/* Multi-day bookings positioned absolutely over the entire row */}
                    {multiDayBookings.map((booking, index) => {
                      const bookingStart = parseISO(booking.date);
                      const bookingEnd = parseISO(booking.end_date);
                      const weekStart = startOfWeek(currentWeek, { weekStartsOn: 1 });
                      
                      // Use date-fns functions for reliable date handling (matching FloorPlanView pattern)
                      let startDayIndex = 0;
                      let endDayIndex = 6;
                      
                      // Find start day index by comparing date strings (avoiding timezone issues)
                      for (let i = 0; i < 7; i++) {
                        const weekDay = addDays(weekStart, i);
                        const weekDayStr = format(weekDay, 'yyyy-MM-dd');
                        const bookingStartStr = format(bookingStart, 'yyyy-MM-dd');
                        if (weekDayStr >= bookingStartStr) {
                          startDayIndex = i;
                          break;
                        }
                      }
                      
                      // Find end day index by comparing date strings (avoiding timezone issues)
                      for (let i = 6; i >= 0; i--) {
                        const weekDay = addDays(weekStart, i);
                        const weekDayStr = format(weekDay, 'yyyy-MM-dd');
                        const bookingEndStr = format(bookingEnd, 'yyyy-MM-dd');
                        if (weekDayStr <= bookingEndStr) {
                          endDayIndex = i;
                          break;
                        }
                      }
                      
                      return (
                        <div 
                          key={`${booking.id}-multi-row`}
                          className={`${getBookingColor(booking.status)} text-white text-xs p-2 rounded cursor-pointer transition-colors absolute z-20`}
                          style={{
                            ...getBookingStyle(booking.status, user.id),
                            gridColumnStart: showUserColumn ? startDayIndex + 2 : startDayIndex + 1,
                            gridColumnEnd: showUserColumn ? endDayIndex + 3 : endDayIndex + 2,
                            top: '50%',
                            transform: `translateY(-50%) translateY(${index * 36}px)`,
                            height: '32px',
                            left: 0,
                            right: 0,
                            marginLeft: '2px',
                            marginRight: '2px'
                          }}
                          title={`${booking.desk.name} (${booking.desk.zone.name}) - ${formatTime(booking.start_time)} to ${formatTime(booking.end_time)} | ${format(bookingStart, 'MMM dd')} - ${format(bookingEnd, 'MMM dd')}`}
                        >
                          <div className="flex items-center gap-2 truncate h-full">
                            <MapPin className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate font-medium">
                              {booking.desk.name}
                            </span>
                            <span className="text-xs opacity-75 ml-auto">
                              {formatTime(booking.start_time)}-{formatTime(booking.end_time)}
                            </span>
                          </div>
                        </div>
                      );
                    })}

                    {showUserColumn && (
                      <div className="p-2 md:p-3 border-r flex items-center gap-2 md:gap-3 bg-muted/20">
                        <Avatar className="h-6 w-6 md:h-8 md:w-8 lg:h-12 lg:w-12 rounded-full overflow-hidden">
                          <AvatarImage src={user.avatar || ''} alt={user.name} className="object-cover rounded-full w-full h-full" />
                          <AvatarFallback className="text-xs md:text-sm">
                            {user.name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-xs md:text-sm lg:text-base truncate">{user.name}</div>
                          <div className="text-xs md:text-sm text-muted-foreground truncate hidden sm:block">
                            {user.department || user.role}
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {weekDays.map((day, dayIndex) => {
                      const dayBookings = getBookingsForUserAndDate(user.id, day);
                      // Filter out multi-day bookings since they're rendered at row level
                      const singleDayBookings = dayBookings.filter(booking => !isMultiDayBooking(booking));

                      return (
                        <div
                          key={`${user.id}-${day.toISOString()}`}
                          className={`p-1 md:p-2 border-r last:border-r-0 relative min-h-[60px] md:min-h-[80px] flex flex-col justify-center ${
                            // Hide some days on smaller screens to match header
                            dayIndex >= 2 && dayIndex < 5 ? 'hidden md:block' : ''
                          } ${
                            dayIndex >= 4 ? 'hidden lg:block' : ''
                          }`}
                        >
                          {/* Render only single day bookings */}
                          {singleDayBookings.map((booking) => {
                            return (
                            <div
                              key={`${booking.id}-single`}
                              className={`${getBookingColor(booking.status)} text-white text-xs p-1 md:p-2 rounded mb-1 cursor-pointer transition-colors relative`}
                              style={getBookingStyle(booking.status, user.id)}
                              title={`${booking.desk.name} (${booking.desk.zone.name}) - ${formatTime(booking.start_time)} to ${formatTime(booking.end_time)}`}
                            >
                              <div className="flex items-center gap-1 md:gap-2 truncate">
                                <MapPin className="h-2 w-2 md:h-3 md:w-3 flex-shrink-0" />
                                <span className="truncate font-medium text-xs">
                                  {booking.desk.name}
                                </span>
                              </div>
                              <div className="text-xs opacity-90 truncate hidden md:block">
                                {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                              </div>
                            </div>
                          )})}
                        </div>
                      );
                    })}
                  </div>
                );
              })
            ) : (
              <div className="p-8 text-center text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No team members or bookings to display</p>
              </div>
            )}
          </div>
        </div>

        {/* Legend */}
        {/* <div className="mt-4 flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-gradient-to-r from-red-400 via-yellow-400 via-green-400 via-blue-400 via-purple-400 to-pink-400 rounded"></div>
            <span>Each user has a unique color</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 bg-red-300 rounded"></div>
            <span>Cancelled (always red)</span>
          </div>
          <div className="text-xs text-muted-foreground">
            • Checked-in bookings appear brighter than regular bookings
          </div>
        </div> */}
      </CardContent>
    </Card>
  );
} 