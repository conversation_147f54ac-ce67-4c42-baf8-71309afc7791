-- Simple SQL executor - minimal function that just runs SELECT queries
-- This replaces the complex execute_query function

-- Drop the old complex function
DROP FUNCTION IF EXISTS execute_query(TEXT) CASCADE;

-- Create a very simple function that just executes the SQL
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    -- Very basic validation - just ensure it starts with SELECT
    IF NOT (TRIM(UPPER(sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries allowed';
    END IF;
    
    -- Execute and return as JSON
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', sql) INTO result;
    
    RETURN COALESCE(result, '[]'::JSON);
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'SQL execution failed: %', SQLERRM;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;

-- Test it
SELECT exec_sql('SELECT 1 as test');
SELECT exec_sql('SELECT ''Development'' as department, 21 as count UNION SELECT ''LAB Team'', 11'); 