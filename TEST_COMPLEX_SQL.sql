-- Test the debug function with the exact SQL that was failing

SELECT 'Testing with the problematic SQL from Gemini:' as test_description;

SELECT * FROM debug_execute_query('SELECT
    u.department,
    COUNT(b.id) AS total_bookings
FROM
    bookings b
JOIN
    users u ON b.user_id = u.id
WHERE
    u.department IS NOT NULL
GROUP BY
    u.department
ORDER BY
    total_bookings DESC;');

-- Also test the robust function with this SQL
SELECT 'Testing robust function with the problematic SQL:' as test_description;

SELECT execute_query('SELECT
    u.department,
    COUNT(b.id) AS total_bookings
FROM
    bookings b
JOIN
    users u ON b.user_id = u.id
WHERE
    u.department IS NOT NULL
GROUP BY
    u.department
ORDER BY
    total_bookings DESC;'); 