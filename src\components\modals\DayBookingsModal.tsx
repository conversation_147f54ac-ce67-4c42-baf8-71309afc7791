import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar as CalendarIcon, Clock, Plus } from 'lucide-react';
import { format } from 'date-fns';

interface DayBookingsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDay: Date | null;
  getBookingsForDay: (date: Date) => any[];
  getAvailableCountForDay: (date: Date) => number;
  formatTime: (timeString: string) => string;
  onBookAvailableDesk: () => void;
}

export function DayBookingsModal({
  isOpen,
  onOpenChange,
  selectedDay,
  getBookingsForDay,
  getAvailableCountForDay,
  formatTime,
  onBookAvailableDesk,
}: DayBookingsModalProps) {
  if (!selectedDay) return null;

  const dayBookings = getBookingsForDay(selectedDay);
  const availableCount = getAvailableCountForDay(selectedDay);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[600px] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            <CalendarIcon className="h-4 w-4 md:h-5 md:w-5" />
            Bookings for {selectedDay ? format(selectedDay, 'PPPP') : ''}
          </DialogTitle>
          <DialogDescription className="text-sm">
            View all bookings and available desks for this day
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 px-1">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-center">
              <p className="text-lg md:text-xl font-bold text-blue-600 dark:text-blue-400">
                {dayBookings?.length || 0}
              </p>
              <p className="text-xs md:text-sm text-muted-foreground">
                {(dayBookings?.length || 0) === 1 ? 'Booking' : 'Bookings'}
              </p>
            </div>
            <div className="p-3 bg-green-50 dark:bg-green-900/20 rounded-lg text-center">
              <p className="text-lg md:text-xl font-bold text-green-600 dark:text-green-400">
                {availableCount}
              </p>
              <p className="text-xs md:text-sm text-muted-foreground">
                Available {availableCount === 1 ? 'Desk' : 'Desks'}
              </p>
            </div>
          </div>

          {/* Bookings List */}
          {dayBookings && dayBookings.length > 0 ? (
            <div>
              <h3 className="font-medium text-sm mb-3 flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Today's Bookings
              </h3>
              <div className="space-y-2">
                {dayBookings.map((booking) => (
                  <div
                    key={booking.id}
                    className="p-3 border rounded-lg bg-muted/30 hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8 md:h-10 md:w-10 flex-shrink-0">
                          <AvatarImage 
                            src={booking.users?.avatar || ''} 
                            alt={booking.users?.name} 
                          />
                          <AvatarFallback className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                            {booking.users?.name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <p className="font-medium text-sm truncate">
                            {booking.users?.name || 'Unknown User'}
                          </p>
                          <p className="text-xs text-muted-foreground truncate">
                            {booking.desks?.[0]?.name} • {booking.desks?.[0]?.zones?.[0]?.name}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                        </p>
                        <Badge 
                          variant="secondary" 
                          className="text-xs"
                        >
                          {booking.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6">
              <CalendarIcon className="h-8 w-8 md:h-12 md:w-12 mx-auto text-muted-foreground mb-3" />
              <p className="text-sm md:text-base text-muted-foreground">
                No bookings for this day
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col gap-2 sm:flex-row pt-4">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Close
          </Button>
          {availableCount > 0 && (
            <Button 
              onClick={onBookAvailableDesk}
              className="w-full sm:w-auto"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Book Available Desk</span>
              <span className="sm:hidden">Book Desk</span>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 