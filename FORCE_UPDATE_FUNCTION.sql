-- Force update the execute_query function to ensure robust version is active

-- Drop the existing function completely
DROP FUNCTION IF EXISTS execute_query(TEXT) CASCADE;

-- Recreate the robust function
CREATE OR REPLACE FUNCTION execute_query(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_result JSONB;
    cleaned_sql TEXT;
    normalized_sql TEXT;
BEGIN
    -- Clean the SQL more thoroughly
    cleaned_sql := query_sql;
    
    -- Remove any BOM or invisible characters
    cleaned_sql := REPLACE(cleaned_sql, chr(65279), ''); -- BOM
    cleaned_sql := REPLACE(cleaned_sql, chr(8203), ''); -- Zero-width space
    cleaned_sql := REPLACE(cleaned_sql, chr(160), ' '); -- Non-breaking space
    
    -- Normalize whitespace and remove trailing semicolons
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, '\s+', ' ', 'g'); -- Multiple spaces to single space
    cleaned_sql := TRIM(cleaned_sql);
    
    -- Remove trailing semicolons (handle multiple semicolons)
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, ';+\s*$', '');
    cleaned_sql := TRIM(cleaned_sql);
    
    -- Create normalized version for validation (remove all whitespace and convert to uppercase)
    normalized_sql := UPPER(REGEXP_REPLACE(cleaned_sql, '\s+', '', 'g'));
    
    -- Check if it's a SELECT query (more flexible)
    IF NOT (normalized_sql LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed. Got: %', LEFT(cleaned_sql, 100);
    END IF;
    
    -- Check for prohibited operations in normalized SQL
    IF normalized_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE)' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for SQL injection patterns in original SQL
    IF cleaned_sql ~* '(xp_cmdshell|sp_executesql|;\s*(DROP|DELETE|UPDATE|INSERT))' THEN
        RAISE EXCEPTION 'Query contains potentially dangerous patterns';
    END IF;
    
    -- Add row limit if not present (max 1000 rows)
    IF NOT (UPPER(cleaned_sql) LIKE '%LIMIT%') THEN
        cleaned_sql := cleaned_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results as JSONB
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', cleaned_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant execute permissions to all authenticated users
GRANT EXECUTE ON FUNCTION execute_query(TEXT) TO authenticated;

-- Test the function to ensure it works
SELECT 'Testing updated execute_query function:' as test_info;
SELECT execute_query('SELECT ''Function Updated Successfully'' as status;');

-- Test with the exact SQL that was failing
SELECT 'Testing with problematic SQL:' as test_info;
SELECT execute_query('SELECT u.department, COUNT(b.id) AS booking_count FROM bookings b JOIN users u ON b.user_id = u.id GROUP BY u.department ORDER BY booking_count DESC;');

-- Verify the function signature
SELECT 
    p.proname as function_name,
    pg_get_function_identity_arguments(p.oid) as arguments,
    p.prosecdef as is_security_definer
FROM pg_proc p
WHERE p.proname = 'execute_query'; 