# SymDesk Reports & Analytics

## Overview
The Reports & Analytics page provides comprehensive insights into workspace utilization and booking patterns. This feature is available exclusively to administrators and offers detailed analytics across multiple dimensions.

## Features

### 📊 Utilization Analytics
- **Daily Utilization Trends**: Line chart showing desk utilization percentage over time
- **Average Utilization**: Overall workspace efficiency metrics
- **Peak Utilization**: Highest utilization days and patterns
- **Active Days**: Days with booking activity

### 🏢 Desk Usage Reports
- **Most Used Desks**: Ranking of desks by booking frequency
- **Utilization Rates**: Individual desk performance metrics
- **Zone Distribution**: Desk usage across different zones
- **Visual Charts**: Bar charts for easy comparison

### 👥 Department Analytics
- **Department Distribution**: Pie chart showing booking share by department
- **User Activity**: Number of active users per department
- **Booking Patterns**: Average bookings per user by department
- **Horizontal Bar Charts**: Department comparison views

### 🏗️ Zone Performance
- **Zone Utilization**: Comparative analysis across zones
- **Floor-wise Metrics**: Performance by floor number
- **Capacity Analysis**: Total desks vs. actual usage
- **Performance Indicators**: High/Medium/Low utilization badges

## Key Metrics

### Summary Statistics
- **Total Bookings**: Aggregate booking count for selected period
- **Average Utilization**: Mean utilization percentage
- **Active Users**: Number of users who made bookings
- **Workspace Capacity**: Total available desks

### Insights Panel
- **Most Active Day**: Day with highest booking activity
- **Most Popular Desk**: Highest utilized desk
- **Top Department**: Department with most bookings

## Date Range Options
- Last 7 days
- Last 30 days
- Last 90 days
- Last year

## Export Functionality
All reports can be exported as CSV files with the following naming convention:
- `utilization_report_[daterange]_[date].csv`
- `desk_usage_report_[daterange]_[date].csv`
- `department_report_[daterange]_[date].csv`
- `zone_report_[daterange]_[date].csv`

## Data Sources
Reports are generated from:
- **Bookings Table**: All booking records with status 'booked' or 'checked-in'
- **Users Table**: User information including department field
- **Desks Table**: Desk configurations and zone assignments
- **Zones Table**: Zone information including floor numbers

## Access Control
- **Admin Only**: Reports are restricted to users with admin role
- **Real-time Data**: All metrics are calculated from live database data
- **Responsive Design**: Optimized for desktop and mobile viewing

## Technical Implementation
- **React Query**: Efficient data fetching and caching
- **Recharts**: Interactive charts and visualizations
- **Supabase**: Real-time database queries
- **TypeScript**: Type-safe data handling
- **Tailwind CSS**: Responsive styling

## Usage Instructions

### For Administrators:
1. Navigate to Admin Dashboard
2. Click on "Reports" in the sidebar or "View Reports" quick action
3. Select desired date range from dropdown
4. Switch between tabs to view different analytics
5. Use Export button to download data as CSV

### Understanding the Data:
- **Utilization %**: (Unique desks booked / Total desks) × 100
- **Booking Count**: Total number of booking records
- **Active Users**: Unique users who made bookings
- **Zone Performance**: Utilization rate per zone with visual indicators

## Future Enhancements
- Real-time dashboard updates
- Predictive analytics
- Custom date range selection
- Advanced filtering options
- Automated report scheduling
- Email report delivery
- Comparative period analysis
- Heat map visualizations
