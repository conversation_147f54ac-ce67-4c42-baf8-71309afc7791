# SymDesk - Tech Stack & Hosting Guide

## Project Overview
SymDesk is a modern desk booking and office management system built with a full-stack TypeScript architecture. This document outlines the complete technology stack and provides guidance for hosting and deployment options.

## 🛠️ Technology Stack

### Frontend Architecture
- **Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 7.0.0 (Lightning-fast build tool and dev server)
- **Styling**: Tailwind CSS 3.4.13 with custom theme configuration
- **UI Components**: 
  - Radix UI primitives (headless, accessible components)
  - shadcn/ui component library
  - Custom animated components with Framer Motion
- **Routing**: React Router DOM 6.20.1
- **State Management**: React Context + TanStack Query (React Query) 5.17.0
- **Form Management**: React Hook Form 7.53.0 with Zod validation
- **Icons**: Lucide React + Radix UI Icons

### Backend & Database
- **Backend-as-a-Service**: Supabase
  - PostgreSQL database with Row Level Security (RLS)
  - Real-time subscriptions
  - Built-in authentication
  - File storage for avatars and zone photos
  - Database migrations management
- **API Client**: Supabase JavaScript SDK 2.39.0
- **Database Schema**:
  - Users (with role-based access: employee, manager, admin)
  - Zones (office areas/floors)
  - Desks (with coordinates and status)
  - Bookings (time-based desk reservations)
  - Settings (company policies and configurations)

### Development Tools
- **Package Manager**: npm
- **Linting**: ESLint 9.11.1 with TypeScript support
- **Code Formatting**: Prettier (via lint-staged)
- **Git Hooks**: Husky 9.1.7 + lint-staged 16.1.2
- **Type Checking**: TypeScript 5.5.3
- **CSS Processing**: PostCSS + Autoprefixer

### Key Features
- **Authentication**: Multi-role user system (Employee/Manager/Admin)
- **Floor Plan Management**: Interactive office layouts with desk positioning
- **Booking System**: Time-based desk reservations with availability checking
- **Team Management**: Manager oversight of team bookings
- **Admin Dashboard**: Full system administration capabilities
- **Responsive Design**: Mobile-first approach with modern UI/UX

## 🚀 Current Hosting Setup

### Vercel (Current)
- **Platform**: Vercel
- **Build Command**: `npm run build` (TypeScript compilation + Vite build)
- **Output Directory**: `dist/`
- **Routing**: SPA routing with catch-all rewrites to `/index.html`
- **Environment Variables Required**:
  - `VITE_SUPABASE_URL`
  - `VITE_SUPABASE_ANON_KEY`

## 🔄 Alternative Hosting Options

### 1. Netlify
**Best for**: Simple deployment with great Git integration
- **Deployment**: Git-based continuous deployment
- **Build Settings**:
  - Build command: `npm run build`
  - Publish directory: `dist`
- **Redirects**: Add `_redirects` file with `/* /index.html 200`
- **Environment Variables**: Set in Netlify dashboard
- **Benefits**: 
  - Easy domain management
  - Built-in form handling
  - Edge functions support
  - Generous free tier

### 2. Firebase Hosting
**Best for**: Google ecosystem integration
- **Setup**: `npm install -g firebase-tools`
- **Configuration**: 
  ```json
  {
    "hosting": {
      "public": "dist",
      "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],
      "rewrites": [{"source": "**", "destination": "/index.html"}]
    }
  }
  ```
- **Deployment**: `firebase deploy`
- **Benefits**:
  - Fast global CDN
  - Custom domains
  - SSL certificates
  - Integration with Google services

### 3. AWS S3 + CloudFront
**Best for**: Enterprise deployments, custom configurations
- **Setup**: S3 bucket for static hosting + CloudFront for CDN
- **Configuration**:
  - S3 bucket with static website hosting
  - CloudFront distribution with custom error pages
  - Route 53 for domain management
- **Deployment**: Can use AWS CLI or GitHub Actions
- **Benefits**:
  - Full AWS ecosystem integration
  - Highly scalable
  - Custom caching strategies
  - Advanced security options

### 4. GitHub Pages
**Best for**: Open source projects, simple hosting
- **Setup**: Enable in repository settings
- **Build**: Use GitHub Actions for build process
- **Limitations**: 
  - Public repositories only (for free)
  - No server-side environment variables
- **Workaround**: Use build-time environment variable injection

### 5. Railway
**Best for**: Full-stack applications, database included
- **Setup**: Connect GitHub repository
- **Build**: Automatic detection of Vite project
- **Benefits**:
  - Can host both frontend and backend
  - Built-in PostgreSQL if needed
  - Environment variable management
  - Automatic deployments

### 6. Digital Ocean App Platform
**Best for**: Developer-friendly cloud deployment
- **Setup**: Connect repository via Digital Ocean dashboard
- **Configuration**: Auto-detected or custom buildpack
- **Benefits**:
  - Integrated with Digital Ocean ecosystem
  - Automatic SSL
  - Multiple deployment environments
  - Cost-effective scaling

## 📋 Handover Checklist

### Required Information for New Host
1. **Supabase Project Details**:
   - Project URL
   - Anon key
   - Service role key (for admin operations)
   - Database password

2. **Environment Variables**:
   ```
   VITE_SUPABASE_URL=your_supabase_project_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

3. **Build Requirements**:
   - Node.js 18+ 
   - npm for package management
   - Build command: `npm run build`
   - Output directory: `dist`

4. **Domain Configuration**:
   - Update CORS settings in Supabase for new domain
   - Configure authentication redirects
   - Update any hardcoded URLs in the application

### Migration Steps
1. **Repository Access**: Transfer GitHub repository ownership or provide access
2. **Supabase Configuration**: 
   - Add new domain to allowed origins
   - Update authentication settings
   - Verify RLS policies
3. **Hosting Platform Setup**:
   - Connect repository to new hosting platform
   - Configure environment variables
   - Set up domain and SSL
4. **Testing**: Verify all functionality works on new domain
5. **DNS Update**: Point domain to new hosting provider

## 🔧 Additional Considerations

### Database Migration
- **Current**: Supabase (PostgreSQL)
- **Migration**: All SQL files in `/supabase/migrations/` directory
- **Data Export**: Use Supabase dashboard or pg_dump
- **Alternative Databases**: Would require significant code changes due to Supabase-specific features

### Performance Optimization
- **Bundle Size**: Currently optimized with Vite's tree-shaking
- **Image Optimization**: Consider adding image optimization service
- **Caching**: Implement proper cache headers for static assets
- **CDN**: Most hosting platforms include CDN by default

### Security Considerations
- **Environment Variables**: Never expose sensitive keys in frontend
- **CORS**: Properly configure for production domains
- **RLS Policies**: Ensure database security policies are properly configured
- **Authentication**: Supabase handles security, but verify settings

## 💰 Cost Comparison

| Platform | Free Tier | Paid Plans Start | Best For |
|----------|-----------|------------------|----------|
| Vercel | 100GB bandwidth | $20/month | React projects |
| Netlify | 100GB bandwidth | $19/month | Static sites |
| Firebase | 10GB storage | Pay-as-you-go | Google ecosystem |
| AWS S3+CloudFront | 12 months free tier | ~$5/month | Enterprise |
| Railway | $5 credit | $5/month | Full-stack apps |
| Digital Ocean | $200 credit | $5/month | Developer-friendly |

## 📞 Support & Maintenance

### Documentation
- All database schema in TypeScript types
- Component documentation in Storybook format (if needed)
- API documentation via Supabase auto-generated docs

### Monitoring
- Consider adding error tracking (Sentry)
- Performance monitoring (Web Vitals)
- Uptime monitoring

### Updates
- Regular dependency updates
- Supabase platform updates
- Security patches

---

**Note**: This project is designed to be hosting-platform agnostic. The main requirement is serving static files with proper SPA routing configuration. The choice of hosting provider should be based on your specific needs regarding cost, performance, and integration requirements.