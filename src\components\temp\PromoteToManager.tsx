import { useState } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { User } from '@/types';

export function PromoteToManager() {
  const [email, setEmail] = useState('');
  const [selectedUserId, setSelectedUserId] = useState('');
  const queryClient = useQueryClient();

  // Fetch all users
  const { data: users } = useQuery({
    queryKey: ['users-for-promotion'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('name');
      
      if (error) throw error;
      return data as User[];
    },
  });

  // Mutation to promote user to manager
  const promoteToManager = useMutation({
    mutationFn: async (userId: string) => {
      const { error } = await supabase
        .from('users')
        .update({ role: 'manager' })
        .eq('id', userId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users-for-promotion'] });
      toast.success('User promoted to manager successfully!');
      setEmail('');
      setSelectedUserId('');
    },
    onError: (error) => {
      toast.error('Failed to promote user: ' + error.message);
    },
  });

  const handlePromoteByEmail = () => {
    if (!email.trim()) {
      toast.error('Please enter an email address');
      return;
    }

    const user = users?.find(u => u.email.toLowerCase() === email.toLowerCase());
    if (!user) {
      toast.error('User not found with that email');
      return;
    }

    if (user.role === 'manager') {
      toast.error('User is already a manager');
      return;
    }

    promoteToManager.mutate(user.id);
  };

  const handlePromoteById = () => {
    if (!selectedUserId) {
      toast.error('Please select a user');
      return;
    }

    const user = users?.find(u => u.id === selectedUserId);
    if (user?.role === 'manager') {
      toast.error('User is already a manager');
      return;
    }

    promoteToManager.mutate(selectedUserId);
  };

  const employeeUsers = users?.filter(u => u.role === 'employee') || [];

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-2xl font-bold">Promote User to Manager</h1>
        <p className="text-muted-foreground">
          Temporary tool to promote users to manager role
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Promote by Email */}
        <Card>
          <CardHeader>
            <CardTitle>Promote by Email</CardTitle>
            <CardDescription>
              Enter the email address of the user to promote
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>
            <Button 
              onClick={handlePromoteByEmail}
              disabled={promoteToManager.isPending}
              className="w-full"
            >
              {promoteToManager.isPending ? 'Promoting...' : 'Promote to Manager'}
            </Button>
          </CardContent>
        </Card>

        {/* Promote by Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Promote by Selection</CardTitle>
            <CardDescription>
              Select a user from the dropdown to promote
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="user-select">Select User</Label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a user to promote" />
                </SelectTrigger>
                <SelectContent>
                  {employeeUsers.map((user) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button 
              onClick={handlePromoteById}
              disabled={promoteToManager.isPending || !selectedUserId}
              className="w-full"
            >
              {promoteToManager.isPending ? 'Promoting...' : 'Promote to Manager'}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Current Managers */}
      <Card>
        <CardHeader>
          <CardTitle>Current Managers</CardTitle>
          <CardDescription>
            Users who currently have manager role
          </CardDescription>
        </CardHeader>
        <CardContent>
          {users?.filter(u => u.role === 'manager').length === 0 ? (
            <p className="text-muted-foreground text-center py-4">
              No managers found. Promote someone to get started!
            </p>
          ) : (
            <div className="space-y-2">
              {users?.filter(u => u.role === 'manager').map((manager) => (
                <div key={manager.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium">{manager.name}</p>
                    <p className="text-sm text-muted-foreground">{manager.email}</p>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Manager since {new Date(manager.updated_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="text-center text-sm text-muted-foreground">
        <p>This is a temporary promotion tool. In production, use the admin panel to manage user roles.</p>
      </div>
    </div>
  );
} 