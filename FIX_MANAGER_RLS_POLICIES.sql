-- ===================================
-- FIX MANAGER RLS POLICIES - INFINITE RECURSION
-- Run this in your Supabase SQL Editor IMMEDIATELY
-- ===================================

-- First, drop all existing policies on users table that might be causing recursion
DROP POLICY IF EXISTS "Managers can view their team members" ON users;
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Admin users can view all users" ON users;
DROP POLICY IF EXISTS "Admin users can update all users" ON users;

-- Now create clean, non-recursive policies
-- 1. Users can always view their own profile
CREATE POLICY "Users can view own profile" ON users
FOR SELECT USING (auth.uid() = id);

-- 2. Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
FOR UPDATE USING (auth.uid() = id);

-- 3. Admin users can view all users (simple check)
CREATE POLICY "Admin users can view all users" ON users
FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- 4. Admin users can update all users
CREATE POLICY "Admin users can update all users" ON users
FOR UPDATE USING (
  EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- 5. Managers can view their direct reports only
CREATE POLICY "Managers can view direct reports" ON users
FOR SELECT USING (
  manager_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  )
);

-- Fix bookings policies too
DROP POLICY IF EXISTS "Managers can view team bookings" ON bookings;

-- Create a simple manager bookings policy
CREATE POLICY "Managers can view team bookings" ON bookings
FOR SELECT USING (
  user_id = auth.uid() OR
  EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
  ) OR
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = bookings.user_id 
    AND users.manager_id = auth.uid()
  )
);

-- Verify policies are working
SELECT 
  schemaname,
  tablename,
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename IN ('users', 'bookings')
ORDER BY tablename, policyname; 