/*
  # Create settings table

  1. New Tables
    - `settings`
      - `id` (uuid, primary key)
      - `company_policies` (jsonb for flexible policy storage)
      - `working_hours` (jsonb for start/end times)
      - `booking_restrictions` (jsonb for flexible restrictions)
      - `created_at` (timestamptz with default)
      - `updated_at` (timestamptz with default)

  2. Security
    - Enable RLS on `settings` table
    - Add policy for authenticated users to read settings
    - Add policy for admins to manage settings

  3. Initial Data
    - Insert default settings record

  4. Triggers
    - Add trigger to automatically update `updated_at` timestamp
*/

-- Create settings table
CREATE TABLE IF NOT EXISTS settings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  company_policies jsonb DEFAULT '{}',
  working_hours jsonb DEFAULT '{"start": "09:00", "end": "17:00"}',
  booking_restrictions jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

-- Enable RLS
ALTER TABLE settings ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Authenticated users can read settings"
  ON settings
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Admins can manage settings"
  ON settings
  FOR ALL
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.role = 'admin'
    )
  );

-- Insert default settings if none exist
INSERT INTO settings (id, company_policies, working_hours, booking_restrictions)
SELECT 
  gen_random_uuid(),
  '{"max_booking_days": 30, "cancellation_hours": 2}',
  '{"start": "09:00", "end": "17:00"}',
  '{"max_advance_days": 14, "max_daily_bookings": 1}'
WHERE NOT EXISTS (SELECT 1 FROM settings);

-- Create trigger for updated_at
CREATE TRIGGER update_settings_updated_at
  BEFORE UPDATE ON settings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();