import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbList, 
  BreadcrumbPage 
} from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Calendar, MapPin, Clock, Plus, LogIn, X, CheckCircle2, Timer, List, Download } from 'lucide-react';
import { format, isToday, differenceInDays, differenceInHours, differenceInMinutes } from 'date-fns';
import { toast } from 'sonner';
import { BookingWithDetails } from '@/types';
import { Link } from 'react-router-dom';
import { BookingsCalendarView } from '@/components/calendar/BookingsCalendarView';
import { generateICSFile, downloadICSFile } from '@/lib/calendar';

export function Bookings() {
  const { appUser } = useAuth();
  const queryClient = useQueryClient();

  const { data: bookings, isLoading } = useQuery({
    queryKey: ['user-bookings', appUser?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          desk:desks (
            *,
            zone:zones (*)
          )
        `)
        .eq('user_id', appUser?.id)
        .order('date', { ascending: false });

      if (error) throw error;
      return data as BookingWithDetails[];
    },
    enabled: !!appUser?.id,
  });

  // Mutation to check in to a booking
  const checkInMutation = useMutation({
    mutationFn: async (bookingId: string) => {
      const { error } = await supabase
        .from('bookings')
        .update({ status: 'checked-in' })
        .eq('id', bookingId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Successfully checked in!');
    },
    onError: (error) => {
      toast.error('Failed to check in: ' + error.message);
    },
  });

  // Mutation to cancel a booking
  const cancelMutation = useMutation({
    mutationFn: async (bookingId: string) => {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      toast.success('Booking cancelled successfully');
    },
    onError: (error) => {
      toast.error('Failed to cancel booking: ' + error.message);
    },
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'booked':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'checked-in':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'cancelled':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'booked':
        return <Clock className="h-4 w-4" />;
      case 'checked-in':
        return <CheckCircle2 className="h-4 w-4" />;
      case 'cancelled':
        return <X className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatTime = (timeString: string) => {
    const time = new Date(`2000-01-01 ${timeString}`);
    return time.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (startDate === endDate) {
      return format(start, 'EEEE, MMM dd, yyyy');
    } else {
      const daysDiff = differenceInDays(end, start) + 1;
      return `${format(start, 'MMM dd')} - ${format(end, 'MMM dd, yyyy')} (${daysDiff} days)`;
    }
  };

  const getBookingDuration = (startTime: string, endTime: string, startDate: string, endDate: string) => {
    if (startDate === endDate) {
      // Same day booking - calculate hours
      const start = new Date(`2000-01-01 ${startTime}`);
      const end = new Date(`2000-01-01 ${endTime}`);
      const hours = differenceInHours(end, start);
      const minutes = differenceInMinutes(end, start) % 60;
      
      if (hours === 0) {
        return `${minutes}m`;
      } else if (minutes === 0) {
        return `${hours}h`;
      } else {
        return `${hours}h ${minutes}m`;
      }
    } else {
      // Multi-day booking
      const days = differenceInDays(new Date(endDate), new Date(startDate)) + 1;
      return `${days} day${days > 1 ? 's' : ''}`;
    }
  };

  const canCheckIn = (booking: BookingWithDetails) => {
    return booking.status === 'booked' && isToday(new Date(booking.date));
  };

  const canCancel = (booking: BookingWithDetails) => {
    // Can cancel if booking is not checked-in/cancelled and the booking hasn't ended yet
    return booking.status === 'booked' && new Date(booking.end_date) >= new Date();
  };

  const upcomingBookings = bookings?.filter(b => 
    new Date(b.end_date) >= new Date() && b.status !== 'cancelled'
  ) || [];

  const pastBookings = bookings?.filter(b => 
    new Date(b.end_date) < new Date() || b.status === 'cancelled'
  ) || [];

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>My Bookings</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">My Bookings</h1>
            <p className="text-muted-foreground">
              View and manage your desk reservations with flexible timing
            </p>
          </div>
          <Button asChild>
            <Link to="/floor-plan">
              <Plus className="h-4 w-4 mr-2" />
              New Booking
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="shadow-clay-sm">
                <CardContent className="p-6">
                  <div className="h-4 bg-muted animate-pulse rounded mb-2" />
                  <div className="h-3 bg-muted animate-pulse rounded mb-4" />
                  <div className="h-3 bg-muted animate-pulse rounded w-1/2" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Tabs defaultValue="list" className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list" className="flex items-center gap-2">
                <List className="h-4 w-4" />
                List View
              </TabsTrigger>
              <TabsTrigger value="calendar" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Calendar View
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-6 mt-6">
            {upcomingBookings.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Upcoming Bookings
                </h2>
                <div className="grid gap-4">
                  {upcomingBookings.map((booking) => (
                    <Card key={booking.id} className="shadow-clay-sm hover:shadow-clay-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-3 flex-1">
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold text-lg">{booking.desk.name}</h3>
                              <Badge className={getStatusColor(booking.status)}>
                                <div className="flex items-center gap-1">
                                  {getStatusIcon(booking.status)}
                                  {booking.status}
                                </div>
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{booking.desk.zone.name} • Floor {booking.desk.zone.floor_number}</span>
                              </div>
                              
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <Calendar className="h-4 w-4" />
                                <span>{formatDateRange(booking.date, booking.end_date)}</span>
                              </div>
                              
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <Timer className="h-4 w-4" />
                                <span>
                                  {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                                  <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                    {getBookingDuration(booking.start_time, booking.end_time, booking.date, booking.end_date)}
                                  </span>
                                </span>
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex gap-2 ml-4">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => {
                                const icsContent = generateICSFile({
                                  deskName: booking.desk.name,
                                  zoneName: booking.desk.zone.name,
                                  startDate: booking.date,
                                  endDate: booking.end_date,
                                  startTime: booking.start_time,
                                  endTime: booking.end_time,
                                  userName: appUser?.name || 'You'
                                });
                                
                                const filename = `desk-booking-${booking.date}.ics`;
                                downloadICSFile(icsContent, filename);
                                toast.success('Calendar event downloaded!');
                              }}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Calendar
                            </Button>
                            
                            {canCheckIn(booking) && (
                              <Button 
                                size="sm"
                                onClick={() => checkInMutation.mutate(booking.id)}
                                disabled={checkInMutation.isPending}
                              >
                                <LogIn className="h-4 w-4 mr-1" />
                                Check In
                              </Button>
                            )}
                            
                            {canCancel(booking) && (
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button size="sm" variant="outline">
                                    <X className="h-4 w-4 mr-1" />
                                    Cancel
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Cancel Booking</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to cancel your booking for {booking.desk.name} from {formatTime(booking.start_time)} to {formatTime(booking.end_time)} on {formatDateRange(booking.date, booking.end_date)}? This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Keep Booking</AlertDialogCancel>
                                    <AlertDialogAction 
                                      onClick={() => cancelMutation.mutate(booking.id)}
                                      className="bg-red-600 hover:bg-red-700"
                                      disabled={cancelMutation.isPending}
                                    >
                                      Cancel Booking
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {pastBookings.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold mb-4">Past Bookings</h2>
                <div className="grid gap-4">
                  {pastBookings.map((booking) => (
                    <Card key={booking.id} className="shadow-clay-sm opacity-75">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between">
                          <div className="space-y-3 flex-1">
                            <div className="flex items-center gap-2">
                              <h3 className="font-semibold">{booking.desk.name}</h3>
                              <Badge className={getStatusColor(booking.status)}>
                                <div className="flex items-center gap-1">
                                  {getStatusIcon(booking.status)}
                                  {booking.status}
                                </div>
                              </Badge>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{booking.desk.zone.name} • Floor {booking.desk.zone.floor_number}</span>
                              </div>
                              
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <Calendar className="h-4 w-4" />
                                <span>{formatDateRange(booking.date, booking.end_date)}</span>
                              </div>
                              
                              <div className="flex items-center gap-2 text-muted-foreground">
                                <Timer className="h-4 w-4" />
                                <span>
                                  {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                                  <span className="ml-2 text-xs bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
                                    {getBookingDuration(booking.start_time, booking.end_time, booking.date, booking.end_date)}
                                  </span>
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {bookings?.length === 0 && (
              <Card className="shadow-clay-sm">
                <CardContent className="p-12 text-center">
                  <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Bookings Yet</h3>
                  <p className="text-muted-foreground mb-4">
                    You haven't made any desk reservations yet. Start by booking a desk with flexible timing!
                  </p>
                  <Button asChild>
                    <Link to="/floor-plan">
                      <Plus className="h-4 w-4 mr-2" />
                      Book Your First Desk
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )}
            </TabsContent>

            <TabsContent value="calendar" className="mt-6">
              <BookingsCalendarView
                bookings={(bookings || []).map(booking => ({
                  id: booking.id,
                  date: booking.date,
                  end_date: booking.end_date,
                  start_time: booking.start_time,
                  end_time: booking.end_time,
                  status: booking.status,
                  user_id: booking.user_id,
                  desk: {
                    name: booking.desk.name,
                    zone: {
                      name: booking.desk.zone.name,
                      floor_number: booking.desk.zone.floor_number
                    }
                  }
                }))}
                users={appUser ? [{
                  id: appUser.id,
                  name: appUser.name || 'You',
                  email: appUser.email || '',
                  avatar: appUser.avatar,
                  role: appUser.role,
                  department: appUser.department
                }] : []}
                currentUserId={appUser?.id}
                title="My Booking Calendar"
                showUserColumn={false}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </>
  );
}