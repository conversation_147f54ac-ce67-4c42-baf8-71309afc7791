import { useTheme } from 'next-themes';
import { useEffect, useState } from 'react';
import SymplexityLightLogo from '@/assets/Symplexity-Black-logo.png';
import SymplexityDarkLogo from '@/assets/Symplexity dark.png';

interface LogoProps {
  className?: string;
  alt?: string;
}

export function Logo({ className = "", alt = "Symplexity Logo" }: LogoProps) {
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Only render after mounting to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return light logo as default during SSR/initial load
    return (
      <img 
        src={SymplexityLightLogo} 
        alt={alt} 
        className={className}
      />
    );
  }

  // Use dark logo for dark theme, light logo for light theme
  const logoSrc = resolvedTheme === 'dark' ? SymplexityDarkLogo : SymplexityLightLogo;

  return (
    <img 
      src={logoSrc} 
      alt={alt} 
      className={className}
    />
  );
} 