import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { generateDeskQRCode } from '@/lib/qrCode';
import { Download, Printer } from 'lucide-react';

interface DeskQRCodeGeneratorProps {
  desk: {
    id: string;
    name: string;
  };
}

export function DeskQRCodeGenerator({ desk }: DeskQRCodeGeneratorProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);

  const generateQRCode = async () => {
    setIsGenerating(true);
    try {
      // Use production domain for QR codes
      const baseUrl = 'https://desk.symplexity.co.za';
      const dataUrl = await generateDeskQRCode(desk.id, desk.name, baseUrl);

      // Create a canvas to overlay the desk name on the QR code
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        if (!ctx) return;

        canvas.width = img.width;
        canvas.height = img.height;

        // Draw the QR code
        ctx.drawImage(img, 0, 0);

        // Create a white background circle in the center
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const radius = Math.min(canvas.width, canvas.height) * 0.15; // 15% of the smaller dimension

        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        ctx.fill();

        // Add a border to the circle
        ctx.strokeStyle = '#000000';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Add the desk name text
        ctx.fillStyle = '#000000';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';

        // Calculate font size based on circle size and text length
        const maxFontSize = radius * 0.8;
        const minFontSize = 12;
        let fontSize = Math.max(minFontSize, Math.min(maxFontSize, radius * 2 / desk.name.length));

        ctx.font = `bold ${fontSize}px Arial`;

        // Check if text fits, if not reduce font size
        let textWidth = ctx.measureText(desk.name).width;
        while (textWidth > radius * 1.6 && fontSize > minFontSize) {
          fontSize -= 1;
          ctx.font = `bold ${fontSize}px Arial`;
          textWidth = ctx.measureText(desk.name).width;
        }

        ctx.fillText(desk.name, centerX, centerY);

        // Convert canvas to data URL
        const finalDataUrl = canvas.toDataURL('image/png');
        setQrCodeUrl(finalDataUrl);
      };

      img.src = dataUrl;
    } catch (error) {
      console.error('Failed to generate QR code:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeUrl) return;
    
    const link = document.createElement('a');
    link.href = qrCodeUrl;
    link.download = `desk-qr-${desk.name}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const printQRCode = () => {
    if (!qrCodeUrl) return;
    
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>Desk QR Code - ${desk.name}</title>
            <style>
              body { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; }
              img { max-width: 300px; }
              h2 { font-family: Arial, sans-serif; }
            </style>
          </head>
          <body>
            <h2>Desk: ${desk.name}</h2>
            <img src="${qrCodeUrl}" alt="Desk QR Code" />
            <p>Scan to check in</p>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.focus();
      printWindow.print();
      printWindow.close();
    }
  };

  return (
    <div className="flex flex-col items-center gap-4">
      {!qrCodeUrl ? (
        <Button onClick={generateQRCode} disabled={isGenerating}>
          {isGenerating ? 'Generating...' : 'Generate QR Code'}
        </Button>
      ) : (
        <>
          <div className="border p-4 rounded-lg">
            <img src={qrCodeUrl} alt="Desk QR Code" className="w-48 h-48" />
          </div>
          <div className="flex gap-2">
            <Button size="sm" onClick={downloadQRCode}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
            <Button size="sm" variant="outline" onClick={printQRCode}>
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </>
      )}
    </div>
  );
}