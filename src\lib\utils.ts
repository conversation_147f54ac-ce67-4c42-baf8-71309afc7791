import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Real-time desk status calculation utility
export function calculateRealtimeStatus(
  deskStatus: 'available' | 'occupied' | 'maintenance',
  deskId: string,
  upcomingBookings: Array<{
    desk_id: string;
    date: string;
    end_date: string;
    start_time: string;
    end_time: string;
    status: string;
  }>
) {
  // If desk is in maintenance, always return maintenance
  if (deskStatus === 'maintenance') {
    return 'maintenance';
  }

  if (!upcomingBookings || upcomingBookings.length === 0) {
    return 'available';
  }

  const now = new Date();
  // Use local date formatting to avoid timezone issues
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const today = `${year}-${month}-${day}`;
  const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

  // Check if desk is currently booked
  const currentBooking = upcomingBookings.find(booking => {
    if (booking.desk_id !== deskId) return false;
    if (!['booked', 'checked-in'].includes(booking.status)) return false;
    
    // Check if booking is for today
    const bookingStartDate = booking.date;
    const bookingEndDate = booking.end_date;
    
    // Check if today falls within the booking date range
    const isWithinDateRange = today >= bookingStartDate && today <= bookingEndDate;
    if (!isWithinDateRange) return false;

    // For bookings that span multiple days, consider them active all day
    if (bookingStartDate !== bookingEndDate) return true;

    // For same-day bookings, check time range
    return currentTime >= booking.start_time && currentTime <= booking.end_time;
  });

  return currentBooking ? 'occupied' : 'available';
}
