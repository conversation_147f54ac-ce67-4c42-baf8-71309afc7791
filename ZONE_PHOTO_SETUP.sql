-- ===================================
-- ZONE PHOTO SETUP SQL
-- Run this in your Supabase SQL Editor
-- ===================================

-- Add photo field to zones table
ALTER TABLE zones ADD COLUMN IF NOT EXISTS photo TEXT;
ALTER TABLE zones ADD COLUMN IF NOT EXISTS description TEXT;

-- Create zone-photos storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('zone-photos', 'zone-photos', true)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Zone photos are publicly accessible" ON storage.objects;
DROP POLICY IF EXISTS "Ad<PERSON> can upload zone photos" ON storage.objects;
DROP POLICY IF EXISTS "Admins can update zone photos" ON storage.objects;
DROP POLICY IF EXISTS "Admins can delete zone photos" ON storage.objects;

-- Create RLS policies for zone-photos bucket
CREATE POLICY "Zone photos are publicly accessible" ON storage.objects 
FOR SELECT USING (bucket_id = 'zone-photos');

CREATE POLICY "Admins can upload zone photos" ON storage.objects 
FOR INSERT WITH CHECK (
  bucket_id = 'zone-photos' AND 
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

CREATE POLICY "Admins can update zone photos" ON storage.objects 
FOR UPDATE USING (
  bucket_id = 'zone-photos' AND 
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

CREATE POLICY "Admins can delete zone photos" ON storage.objects 
FOR DELETE USING (
  bucket_id = 'zone-photos' AND 
  auth.uid() IS NOT NULL AND
  EXISTS (
    SELECT 1 FROM users 
    WHERE users.id = auth.uid() 
    AND users.role = 'admin'
  )
);

-- Enable RLS on storage.objects (if not already enabled)
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'zones' 
AND column_name IN ('photo', 'description')
ORDER BY ordinal_position; 