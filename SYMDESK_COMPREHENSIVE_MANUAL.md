# SymDesk Comprehensive User Manual

**Version 1.0 | 2025**  
**For Employees, Managers, and Administrators**

---

## Table of Contents

1. [Introduction](#introduction)
2. [Getting Started](#getting-started)
3. [Employee Guide](#employee-guide)
4. [Manager Guide](#manager-guide)
5. [Administrator Guide](#administrator-guide)
6. [Troubleshooting](#troubleshooting)
7. [Support](#support)

---

## Introduction

SymDesk is a modern desk booking and office management system designed to streamline workspace allocation and improve office efficiency. The system supports three user roles with different capabilities:

- **Employees**: Book desks, manage personal bookings, check-in via QR codes
- **Managers**: Monitor team bookings, view team analytics, manage direct reports
- **Administrators**: Full system management, user administration, workspace configuration

### Key Features
- Interactive floor plan visualization
- Real-time desk availability
- QR code check-in system
- Team management and analytics
- Calendar integration
- Mobile-responsive design

---

## Getting Started

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection
- Company email address (@symplexity.co.za)

### Creating Your Account

1. **Navigate to SymDesk**: Visit https://desk.symplexity.co.za/
2. **Click "Sign Up"** on the login page
3. **Fill out the registration form**:
   - **Full Name**: Your first and last name
   - **Email**: Must use your @symplexity.co.za email address
   - **Password**: Minimum 6 characters (use a strong password)
   - **Manager**: Select your direct manager from the dropdown (optional)
4. **Submit the form** and check your email
5. **Confirm your email**: Click the verification link sent to your inbox
6. **Return to SymDesk** and sign in with your credentials

### First Login Setup

1. **Complete your profile**:
   - Navigate to Profile in the sidebar
   - Click "Edit Profile"
   - Add department, location, phone number, and bio
   - Upload a professional profile picture
2. **Familiarize yourself** with the dashboard and navigation

---

## Employee Guide

### Dashboard Overview

The employee dashboard provides:
- **Quick Stats**: Your upcoming bookings, total bookings, favorite desk
- **Today's Bookings**: Current desk reservations with check-in options
- **Upcoming Bookings**: Future reservations with management options
- **Quick Actions**: Direct links to book desks and view bookings

### Booking a Desk

#### Method 1: Floor Plan View

1. **Navigate to "Book a Desk"** in the sidebar
2. **Select Floor Plan view** (default)
3. **Choose your date** using the date picker
4. **View desk availability**:
   - **Green**: Available desks
   - **Red**: Occupied/booked desks
   - **Yellow**: Maintenance or unavailable
5. **Click on an available desk** to see details
6. **Click "Book This Desk"** in the modal
7. **Configure your booking**:
   - Start and end dates
   - Start and end times (default: 8:00 AM - 5:00 PM)
   - Option to download calendar event
8. **Confirm booking**

#### Method 2: Calendar View

1. **Switch to Calendar view**
2. **Click on any date** to see available desks
3. **Select "Book a Desk"** from the day modal
4. **Choose from available desks** for that day
5. **Complete booking configuration**

### Managing Your Bookings

#### Viewing Bookings

1. **Go to "My Bookings"** in the sidebar
2. **View all bookings** in a comprehensive list
3. **Filter by status**: All, Upcoming, Past, Cancelled
4. **See booking details**: Desk name, zone, date, time, status

#### Modifying Bookings

- **Cancel**: Click the cancel button on any future booking
- **Check-in**: Use the check-in button for today's bookings


### Checking In

#### QR Code Check-in (Recommended)

1. **Locate the QR code** on your assigned desk
2. **Scan with your phone camera** or QR code app
3. **Open the check-in link** in your browser
4. **Verify your booking details**
5. **Click "Check In Now"**
6. **Confirmation**: You'll see a success message

#### Manual Check-in

1. **Go to Dashboard** or "My Bookings"
2. **Find today's booking**
3. **Click "Check In"** button
4. **Confirm check-in**

### Profile Management

1. **Access Profile** from the sidebar
2. **Edit Profile** to update:
   - Personal information
   - Department and location
   - Contact details
   - Bio and profile picture
3. **Upload Avatar**: Click camera icon to change profile picture
4. **Save Changes**

---

## Manager Guide

### Manager Dashboard

Managers have access to additional features for team oversight:

#### Team Overview
- **Team Statistics**: Size, today's bookings, upcoming bookings
- **Desk Utilization**: Available vs. occupied desks
- **Team Activity**: Recent booking activity

#### Team Management

1. **Navigate to "Team Dashboard"** in the sidebar
2. **View team members**: All direct reports are listed
3. **Access team member details**:
   - Contact information
   - Booking history
   - Favorite desks and zones
   - Recent activity

### Monitoring Team Bookings

#### Team Booking Analytics

- **Total Bookings**: Historical booking count per team member
- **Upcoming Bookings**: Future reservations
- **Favorite Locations**: Most frequently booked desks/zones
- **Last Activity**: Recent booking activity

#### Team Calendar View

1. **Use the Organization Calendar** tab
2. **View team bookings** across different dates
3. **Identify patterns** in office attendance
4. **Plan team meetings** based on office presence

### Team Coordination

#### Best Practices for Managers

1. **Regular Check-ins**: Monitor team office attendance patterns
2. **Space Planning**: Ensure adequate desk availability for team days
3. **Communication**: Coordinate with team for important office days
4. **Reporting**: Use analytics to optimize office space usage

### Privacy and Permissions

- **Data Access**: Managers can only see their direct reports' data
- **Booking Visibility**: View all team member bookings (past and future)
- **Profile Access**: See team member profiles and contact information
- **No Modification**: Managers cannot modify team member bookings

---

## Administrator Guide

### Admin Dashboard

Administrators have full system access and management capabilities:

#### System Overview
- **User Statistics**: Total users, admins, employees, managers
- **Desk Statistics**: Total desks, available, occupied, maintenance
- **Booking Analytics**: Recent bookings, system usage
- **Zone Management**: Office areas and floor configuration

### User Management

#### Adding New Users

New users join the system through the self-registration process:

1. **Direct users to the registration page**: https://desk.symplexity.co.za/
2. **Users complete self-registration**:
   - Must use @symplexity.co.za email address
   - Fill out personal details and select their manager
   - Verify email address through confirmation link
3. **Admin assigns appropriate roles**: After registration, use the user management interface to adjust roles as needed

#### Managing Existing Users

1. **View all users** in the user management interface
2. **Change roles**: Promote employees to managers or admins using the role dropdown
3. **Manage team assignments**: Assign employees to managers using the manager dropdown
4. **Search users**: Use the search functionality to quickly find specific users
5. **Monitor user statistics**: View counts of total users, administrators, managers, and employees

### Workspace Management

#### Zone Configuration

1. **Go to "Manage Zones"**
2. **Create new zones**:
   - Zone name and description
   - Floor number
   - Upload zone photos
   - Set zone capacity
3. **Edit existing zones**: Modify details and photos
4. **Delete zones**: Remove unused areas (if no desks assigned)

#### Desk Management

1. **Navigate to "Configure Desks"**
2. **Add new desks**:
   - Desk name/identifier
   - Assign to zone
   - Set coordinates for floor plan
   - Initial status (available/maintenance)
3. **Manage existing desks**:
   - Edit desk details
   - Change status (available/maintenance/occupied)
   - Generate QR codes for check-in
   - Move between zones
4. **Bulk operations**: Import/export desk configurations

#### QR Code Management

1. **Access desk management**
2. **Select individual desks**
3. **Generate QR codes**:
   - Automatic generation with desk identifier
   - Download as image files
   - Print directly from browser
   - Bulk generation for multiple desks
4. **QR Code Features**:
   - Contains desk name in center for easy identification
   - Links to check-in page with desk ID
   - High error correction for reliability

### System Settings

#### Booking Policies

1. **Configure booking rules**:
   - Maximum advance booking period (default: 90 days)
   - Minimum booking duration
   - Maximum booking duration
   - Cancellation policies
2. **Set office hours**: Default booking times
3. **Holiday management**: Block booking on company holidays

#### Security and Access

1. **Email domain restrictions**: Currently limited to @symplexity.co.za
2. **Role-based access control**: Manage permissions by role
3. **Data privacy**: Ensure compliance with company policies

### Analytics and Reporting

#### Usage Analytics

1. **Desk Utilization**: Track which desks are most/least used
2. **User Activity**: Monitor booking patterns by user
3. **Zone Popularity**: Identify preferred office areas
4. **Time-based Analysis**: Peak booking times and days

#### System Health

1. **Monitor system performance**
2. **Track user adoption**
3. **Identify system issues**
4. **Generate usage reports**

### Maintenance Tasks

#### Regular Maintenance

1. **User Account Reviews**: Quarterly review of active users
2. **Desk Status Updates**: Regular maintenance scheduling
3. **Zone Photo Updates**: Keep floor plan images current
4. **System Backups**: Ensure data protection
5. **Performance Monitoring**: Track system responsiveness

---

## Troubleshooting

### Common Issues

#### Login Problems

**Issue**: Cannot sign in with credentials
**Solutions**:
1. Verify email address is @symplexity.co.za
2. Check if email is confirmed (check spam folder)
3. Try password reset
4. Contact administrator

**Issue**: Account not found
**Solutions**:
1. Verify correct email address
2. Check if account was created by administrator
3. Contact IT support

#### Booking Issues

**Issue**: Cannot book desk
**Solutions**:
1. Check if desk is available for selected date
2. Verify booking is within 90-day limit
3. Ensure not booking in the past
4. Try refreshing the page

**Issue**: QR code not working
**Solutions**:
1. Ensure you have an active booking for today
2. Check if booking status is "booked" (not cancelled)
3. Verify you're scanning the correct desk's QR code
4. Try manual check-in from dashboard

#### Performance Issues

**Issue**: Slow loading
**Solutions**:
1. Check internet connection
2. Clear browser cache
3. Try different browser
4. Contact IT if problem persists

### Error Messages

- **"Only @symplexity.co.za email addresses are allowed"**: Use company email
- **"Cannot book desks for past dates"**: Select future date
- **"Desk already booked"**: Choose different desk or date
- **"Access Denied"**: Contact administrator for role permissions

---

## Support

### Getting Help

#### Self-Service Resources
1. **This manual**: Comprehensive guide for all features
2. **In-app tooltips**: Hover over interface elements for help
3. **Dashboard quick actions**: Direct links to common tasks

#### Contact Support

**IT Support**: For technical issues
- Email: <EMAIL>
- Phone: [Internal Extension]

**System Administrator**: For account and permission issues
- Email: <EMAIL>
- Internal contact: [Administrator Name]

#### Reporting Issues

When reporting issues, please include:
1. **User role**: Employee, Manager, or Admin
2. **Browser and version**: Chrome 120, Firefox 115, etc.
3. **Error message**: Exact text of any error
4. **Steps to reproduce**: What you were doing when issue occurred
5. **Screenshots**: If applicable

### Feature Requests

Submit feature requests to: <EMAIL>

Include:
- **Description**: What feature you'd like
- **Use case**: Why it would be helpful
- **Priority**: How important it is to your workflow

---

**Document Version**: 1.1
**Last Updated**: 2025-07-03
**Next Review**: 2025-10-03

For the most current version of this manual, visit the SymDesk help section or contact your system administrator.
