-- ===================================
-- FIX ZONE PHOTO RLS ERRORS
-- Run this in your Supabase SQL Editor
-- ===================================

-- Ensure the bucket exists and is public
INSERT INTO storage.buckets (id, name, public) 
VALUES ('zone-photos', 'zone-photos', true)
ON CONFLICT (id) DO UPDATE SET public = true;

-- Remove any existing problematic policies
DO $$ 
BEGIN
    -- Drop policies if they exist (ignore errors if they don't exist)
    BEGIN
        DROP POLICY IF EXISTS "Zone photos are publicly accessible" ON storage.objects;
        DROP POLICY IF EXISTS "Ad<PERSON> can upload zone photos" ON storage.objects;
        DROP POLICY IF EXISTS "Ad<PERSON> can update zone photos" ON storage.objects;
        DROP POLICY IF EXISTS "Admins can delete zone photos" ON storage.objects;
        DROP POLICY IF EXISTS "Public Zone Photo Access" ON storage.objects;
        DROP POLICY IF EXISTS "Authenticated Zone Photo Upload" ON storage.objects;
        DROP POLICY IF EXISTS "Authenticated Zone Photo Update" ON storage.objects;
        DROP POLICY IF EXISTS "Authenticated Zone Photo Delete" ON storage.objects;
    EXCEPTION 
        WHEN others THEN 
            NULL; -- Ignore any errors
    END;
END $$;

-- Create simple, permissive policies that should work
DO $$
BEGIN
    -- Allow public read access to zone photos
    BEGIN
        EXECUTE format('CREATE POLICY %I ON storage.objects FOR SELECT USING (bucket_id = %L)', 
                      'zone_photos_public_read', 'zone-photos');
    EXCEPTION 
        WHEN duplicate_object THEN 
            NULL; -- Policy already exists
    END;

    -- Allow authenticated users to upload zone photos
    BEGIN
        EXECUTE format('CREATE POLICY %I ON storage.objects FOR INSERT WITH CHECK (bucket_id = %L AND auth.role() = %L)', 
                      'zone_photos_auth_upload', 'zone-photos', 'authenticated');
    EXCEPTION 
        WHEN duplicate_object THEN 
            NULL; -- Policy already exists
    END;

    -- Allow authenticated users to update zone photos
    BEGIN
        EXECUTE format('CREATE POLICY %I ON storage.objects FOR UPDATE USING (bucket_id = %L AND auth.role() = %L)', 
                      'zone_photos_auth_update', 'zone-photos', 'authenticated');
    EXCEPTION 
        WHEN duplicate_object THEN 
            NULL; -- Policy already exists
    END;

    -- Allow authenticated users to delete zone photos
    BEGIN
        EXECUTE format('CREATE POLICY %I ON storage.objects FOR DELETE USING (bucket_id = %L AND auth.role() = %L)', 
                      'zone_photos_auth_delete', 'zone-photos', 'authenticated');
    EXCEPTION 
        WHEN duplicate_object THEN 
            NULL; -- Policy already exists
    END;
END $$;

-- Verify setup
SELECT 
    'zone-photos bucket' as item,
    CASE WHEN EXISTS(SELECT 1 FROM storage.buckets WHERE id = 'zone-photos') 
         THEN 'EXISTS' ELSE 'MISSING' END as status,
    CASE WHEN EXISTS(SELECT 1 FROM storage.buckets WHERE id = 'zone-photos' AND public = true) 
         THEN 'PUBLIC' ELSE 'NOT PUBLIC' END as public_status;

-- Show existing policies for the bucket
SELECT 
    policyname,
    cmd,
    permissive,
    roles,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'objects' 
AND policyname LIKE '%zone%'; 