-- This is the EXACT query that ManageTeam.tsx is running
-- Test this in SQL Editor to see what we get

-- 1. First, let's get the team member IDs (replace with actual IDs from your users table)
SELECT id, name, role FROM users WHERE role != 'admin' ORDER BY name;

-- 2. Now the actual bookings query that's failing
-- Replace the user IDs in the IN clause with actual team member IDs from step 1
SELECT
  b.id,
  b.date,
  b.end_date,
  b.start_time,
  b.end_time,
  b.status,
  b.user_id,
  b.desk_id,
  d.name as desk_name,
  z.name as zone_name,
  z.floor_number
FROM bookings b
LEFT JOIN desks d ON b.desk_id = d.id
LEFT JOIN zones z ON d.zone_id = z.id
WHERE b.user_id IN (
  -- Replace these with actual user IDs from step 1
  '4a3b82ea-c364-440e-90a9-f5efbed0a870'
  -- Add more team member IDs here separated by commas
)
AND b.date >= (CURRENT_DATE - INTERVAL '30 days')
ORDER BY b.date ASC;

-- 3. Let's also check the specific booking that was showing "Unknown Desk"
SELECT
  b.id,
  b.date,
  b.end_date,
  b.start_time,
  b.end_time,
  b.status,
  b.user_id,
  b.desk_id,
  d.name as desk_name,
  d.status as desk_status,
  z.name as zone_name,
  z.floor_number
FROM bookings b
LEFT JOIN desks d ON b.desk_id = d.id
LEFT JOIN zones z ON d.zone_id = z.id
WHERE b.id = '2868707b-a7b3-4881-8ab6-e4a699149e30';

-- 4. Let's verify the desk exists and check its relationships
SELECT 
  d.id as desk_id,
  d.name as desk_name,
  d.status,
  d.zone_id,
  z.name as zone_name,
  z.floor_number
FROM desks d
LEFT JOIN zones z ON d.zone_id = z.id
WHERE d.id = 'e10a0280-16c0-464b-9cf3-329b4f6c732e';

-- 5. Check if there are any issues with the Supabase query format
-- This mimics exactly what Supabase would do:
SELECT json_build_object(
  'id', b.id,
  'date', b.date,
  'end_date', b.end_date,
  'start_time', b.start_time,
  'end_time', b.end_time,
  'status', b.status,
  'user_id', b.user_id,
  'desk_id', b.desk_id,
  'desk', json_build_object(
    'name', d.name,
    'zone', json_build_object(
      'name', z.name,
      'floor_number', z.floor_number
    )
  )
) as booking_data
FROM bookings b
LEFT JOIN desks d ON b.desk_id = d.id
LEFT JOIN zones z ON d.zone_id = z.id
WHERE b.id = '2868707b-a7b3-4881-8ab6-e4a699149e30'; 