import { ReactNode } from 'react';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { AppSidebar } from './AppSidebar';

interface LayoutProps {
  children: ReactNode;
}

export function Layout({ children }: LayoutProps) {
  return (
    <SidebarProvider className="h-screen w-screen">
      <AppSidebar />
      <SidebarInset className="flex flex-col h-screen overflow-hidden">
        <main className="flex-1 overflow-auto p-6 pt-8">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}