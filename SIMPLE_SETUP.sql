-- Simple setup for AI Query system using normal Supabase operations
-- This replaces all the complex database functions

-- Ensure tables exist (they might already exist)
CREATE TABLE IF NOT EXISTS ai_query_rate_limits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    query_count INTEGER DEFAULT 0,
    last_reset_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS ai_query_audit (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    natural_language_query TEXT NOT NULL,
    generated_sql TEXT NOT NULL,
    execution_time_ms INTEGER,
    success BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    result_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE UNIQUE INDEX IF NOT EXISTS idx_ai_query_rate_limits_user_id ON ai_query_rate_limits(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_query_audit_user_id ON ai_query_audit(user_id);
CREATE INDEX IF NOT EXISTS idx_ai_query_audit_created_at ON ai_query_audit(created_at);

-- Enable RLS
ALTER TABLE ai_query_rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_query_audit ENABLE ROW LEVEL SECURITY;

-- Simple RLS policies
DROP POLICY IF EXISTS "Users can manage their own rate limits" ON ai_query_rate_limits;
CREATE POLICY "Users can manage their own rate limits" ON ai_query_rate_limits
    FOR ALL USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can manage their own audit records" ON ai_query_audit;
CREATE POLICY "Users can manage their own audit records" ON ai_query_audit
    FOR ALL USING (auth.uid() = user_id);

-- Simple SQL executor function
DROP FUNCTION IF EXISTS exec_sql(TEXT) CASCADE;
CREATE OR REPLACE FUNCTION exec_sql(sql TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    -- Basic validation - just ensure it starts with SELECT
    IF NOT (TRIM(UPPER(sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries allowed';
    END IF;
    
    -- Execute and return as JSON
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', sql) INTO result;
    
    RETURN COALESCE(result, '[]'::JSON);
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'SQL execution failed: %', SQLERRM;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION exec_sql(TEXT) TO authenticated;

-- Test the setup
SELECT 'Testing simple setup:' as test_info;
SELECT exec_sql('SELECT ''Setup completed successfully'' as status, ''Development'' as department, 21 as bookings'); 