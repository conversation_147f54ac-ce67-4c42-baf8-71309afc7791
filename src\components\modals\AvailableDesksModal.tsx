import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Grid } from 'lucide-react';
import { format } from 'date-fns';
import { DeskWithZone } from '@/types';

interface AvailableDesksModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDay: Date | null;
  getAvailableDesksForDay: (date: Date) => DeskWithZone[];
  onSelectDesk: (desk: DeskWithZone) => void;
}

export function AvailableDesksModal({
  isOpen,
  onOpenChange,
  selectedDay,
  getAvailableDesksForDay,
  onSelectDesk,
}: AvailableDesksModalProps) {
  if (!selectedDay) return null;

  const availableDesks = getAvailableDesksForDay(selectedDay);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[600px] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            <Grid className="h-4 w-4 md:h-5 md:w-5" />
            Available Desks for {selectedDay ? format(selectedDay, 'PPPP') : ''}
          </DialogTitle>
          <DialogDescription className="text-sm">
            Select a desk to book for this day
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 px-1">
          {availableDesks.length > 0 ? (
            <div className="grid gap-3">
              {availableDesks.map((desk) => (
                <Button
                  key={desk.id}
                  variant="outline"
                  onClick={() => onSelectDesk(desk)}
                  className="h-auto p-4 justify-start text-left hover:bg-green-50 hover:border-green-300 dark:hover:bg-green-900/20"
                >
                  <div className="flex items-center gap-3 w-full">
                    <div className="h-8 w-8 md:h-10 md:w-10 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Grid className="h-4 w-4 md:h-5 md:w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm md:text-base truncate">{desk.name}</p>
                      <p className="text-xs md:text-sm text-muted-foreground truncate">
                        {desk.zone.name} • Floor {desk.zone.floor_number}
                      </p>
                    </div>
                    <Badge variant="secondary" className="text-xs">
                      Available
                    </Badge>
                  </div>
                </Button>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <Grid className="h-8 w-8 md:h-12 md:w-12 mx-auto text-muted-foreground mb-3" />
              <p className="text-sm md:text-base text-muted-foreground">
                No available desks for this day
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col gap-2 sm:flex-row pt-4">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="w-full sm:w-auto"
          >
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 