/*
  # Add INSERT policy for users table

  1. Security
    - Add policy for users to insert their own profile data during sign-up
    - Policy allows both authenticated and anonymous users to insert records where the id matches their auth.uid()
    - This enables the sign-up flow to create user profiles successfully

  2. Changes
    - Add INSERT policy "Users can create own profile" for users table
    - Policy applies to both authenticated and anon roles
    - Uses auth.uid() = id to ensure users can only create their own profile
*/

-- Add INSERT policy for users table to allow profile creation during sign-up
CREATE POLICY "Users can create own profile"
  ON users
  FOR INSERT
  TO authenticated, anon
  WITH CHECK (auth.uid() = id);