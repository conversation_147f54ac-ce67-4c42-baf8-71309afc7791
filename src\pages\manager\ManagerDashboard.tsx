import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Users, Calendar, MapPin, Clock, User, TrendingUp } from 'lucide-react';
import { format, isToday, isFuture } from 'date-fns';
import { Link } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  avatar?: string;
}

interface TeamBooking {
  id: string;
  date: string;
  end_date: string;
  start_time: string;
  end_time: string;
  status: string;
  user_id: string;
  user_name: string;
  user_avatar?: string;
  desk_name: string;
  zone_name: string;
  floor_number: number;
}

export function ManagerDashboard() {
  const { appUser } = useAuth();
  const isMobile = useIsMobile();

  // Fetch team members
  const { data: teamMembers, isLoading: teamLoading } = useQuery({
    queryKey: ['team-members', appUser?.id, appUser?.role],
    queryFn: async () => {
      if (!appUser?.id) throw new Error('No user ID');

      let query = supabase
        .from('users')
        .select('id, name, email, role, department, avatar');

      // If admin, show all users except admins; if manager, show only direct reports
      if (appUser.role === 'admin') {
        query = query.neq('role', 'admin');
      } else {
        query = query.eq('manager_id', appUser.id);
      }

      const { data, error } = await query.order('name');

      if (error) throw error;
      return data as TeamMember[];
    },
    enabled: !!appUser?.id && (appUser?.role === 'manager' || appUser?.role === 'admin'),
  });

  // Fetch team statistics
  const { data: teamStats, isLoading: statsLoading } = useQuery({
    queryKey: ['team-stats', appUser?.id],
    queryFn: async () => {
      if (!appUser?.id || !teamMembers) return null;

      const teamMemberIds = teamMembers.map(member => member.id);
      if (teamMemberIds.length === 0) return null;

      const today = new Date().toISOString().split('T')[0];

      const [bookingsResult, desksResult] = await Promise.all([
        supabase
          .from('bookings')
          .select('*')
          .in('user_id', teamMemberIds)
          .gte('date', today),
        supabase
          .from('desks')
          .select('status')
      ]);

      const bookings = bookingsResult.data || [];
      const desks = desksResult.data || [];

      const todayBookings = bookings.filter(b => b.date === today && b.status !== 'cancelled');
      const upcomingBookings = bookings.filter(b => b.date > today && b.status !== 'cancelled');
      const activeBookings = bookings.filter(b => b.status === 'booked' || b.status === 'checked-in');

      return {
        teamSize: teamMembers.length,
        todayBookings: todayBookings.length,
        upcomingBookings: upcomingBookings.length,
        activeBookings: activeBookings.length,
        totalDesks: desks.length,
        availableDesks: desks.filter(d => d.status === 'available').length,
      };
    },
    enabled: !!appUser?.id && !!teamMembers,
  });

  // Fetch team bookings
  const { data: teamBookings, isLoading: bookingsLoading } = useQuery({
    queryKey: ['team-bookings', appUser?.id],
    queryFn: async () => {
      if (!appUser?.id || !teamMembers) return [];

      const teamMemberIds = teamMembers.map(member => member.id);
      if (teamMemberIds.length === 0) return [];

      const today = new Date().toISOString().split('T')[0];

      // Step 1: Get basic bookings data first
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select('*')
        .in('user_id', teamMemberIds)
        .gte('date', today)
        .in('status', ['booked', 'checked-in'])
        .order('date', { ascending: true })
        .limit(20);

      if (bookingsError) {
        console.error('Bookings query error:', bookingsError);
        throw bookingsError;
      }

      if (!bookingsData || bookingsData.length === 0) {
        return [];
      }

      // Step 2: Get users data
      const userIds = [...new Set(bookingsData.map(b => b.user_id))];
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, name, avatar')
        .in('id', userIds);

      if (usersError) {
        console.error('Users query error:', usersError);
      }

      // Step 3: Get desks and zones data
      const deskIds = [...new Set(bookingsData.map(b => b.desk_id))];
      const { data: desksData, error: desksError } = await supabase
        .from('desks')
        .select(`
          id,
          name,
          zone_id,
          zones (
            name,
            floor_number
          )
        `)
        .in('id', deskIds);

      if (desksError) {
        console.error('Desks query error:', desksError);
      }

      // Step 4: Combine the data
      return bookingsData.map((booking) => {
        const user = usersData?.find(u => u.id === booking.user_id);
        const desk = desksData?.find(d => d.id === booking.desk_id);
        const zone = (desk as any)?.zones;

        return {
          id: booking.id,
          date: booking.date,
          end_date: booking.end_date,
          start_time: booking.start_time,
          end_time: booking.end_time,
          status: booking.status,
          user_id: booking.user_id,
          user_name: user?.name || 'Unknown',
          user_avatar: user?.avatar,
          desk_name: desk?.name || 'Unknown',
          zone_name: zone?.name || 'Unknown',
          floor_number: zone?.floor_number || 0,
        } as TeamBooking;
      });
    },
    enabled: !!appUser?.id && !!teamMembers,
  });

  const formatTime = (timeString: string) => {
    const time = new Date(`2000-01-01 ${timeString}`);
    return time.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    
    if (startDate === endDate) {
      return format(start, 'MMM dd');
    } else {
      return `${format(start, 'MMM dd')} - ${format(end, 'MMM dd')}`;
    }
  };

  const getBookingStatus = (booking: TeamBooking) => {
    const bookingDate = new Date(booking.date);
    if (isToday(bookingDate)) {
      return { label: 'Today', variant: 'default' as const };
    } else if (isFuture(bookingDate)) {
      return { label: 'Upcoming', variant: 'secondary' as const };
    } else {
      return { label: 'Past', variant: 'outline' as const };
    }
  };

  if (appUser?.role !== 'manager' && appUser?.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Access Denied</h2>
          <p className="text-muted-foreground">Manager or admin access required</p>
        </div>
      </div>
    );
  }

  if (teamLoading || statsLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="px-1">
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">
          {appUser?.role === 'admin' ? 'Organization Dashboard' : 'Team Dashboard'}
        </h1>
        <p className="text-sm sm:text-base text-muted-foreground mt-1">
          {appUser?.role === 'admin'
            ? 'Monitor organization-wide workspace activity and bookings'
            : 'Monitor your team\'s workspace activity and bookings'
          }
        </p>
      </div>

      {/* Team Stats */}
      <div className="grid gap-3 sm:gap-4 grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Team Size</CardTitle>
            <Users className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-xl sm:text-2xl font-bold">{teamStats?.teamSize || 0}</div>
            <p className="text-xs text-muted-foreground">
              Direct reports
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Today's Bookings</CardTitle>
            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-xl sm:text-2xl font-bold">{teamStats?.todayBookings || 0}</div>
            <p className="text-xs text-muted-foreground">
              Team members in office
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Upcoming Bookings</CardTitle>
            <Clock className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-xl sm:text-2xl font-bold">{teamStats?.upcomingBookings || 0}</div>
            <p className="text-xs text-muted-foreground">
              Future reservations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-xs sm:text-sm font-medium">Office Utilization</CardTitle>
            <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="pb-3">
            <div className="text-xl sm:text-2xl font-bold">
              {teamStats?.teamSize && teamStats?.todayBookings
                ? Math.round((teamStats.todayBookings / teamStats.teamSize) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Team in office today
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 sm:gap-6 lg:grid-cols-2">
        {/* Team Members */}
        <Card>
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="text-lg sm:text-xl">Team Members</CardTitle>
            <CardDescription className="text-sm">
              Your direct reports and their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            {teamMembers && teamMembers.length > 0 ? (
              <div className="space-y-2 sm:space-y-3">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 rounded-lg border">
                    <Avatar className="h-8 w-8 sm:h-10 sm:w-10 flex-shrink-0">
                      <AvatarImage src={member.avatar || ''} alt={member.name} />
                      <AvatarFallback className="text-xs sm:text-sm">
                        {member.name?.charAt(0)?.toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-sm sm:text-base truncate">{member.name}</p>
                      <p className="text-xs sm:text-sm text-muted-foreground truncate">{member.email}</p>
                      {member.department && (
                        <p className="text-xs text-muted-foreground truncate">{member.department}</p>
                      )}
                    </div>
                    <Badge variant="outline" className="capitalize text-xs flex-shrink-0">
                      {member.role}
                    </Badge>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6 sm:py-8 text-muted-foreground">
                <Users className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 sm:mb-4 opacity-50" />
                <p className="text-sm sm:text-base">No team members assigned</p>
                <p className="text-xs sm:text-sm">Contact admin to assign employees to your team</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-3 sm:pb-6">
            <CardTitle className="text-lg sm:text-xl">Quick Actions</CardTitle>
            <CardDescription className="text-sm">
              Common team management tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2 sm:space-y-3">
            <Button asChild className="w-full justify-start text-sm h-9 sm:h-10">
              <Link to="/floor-plan">
                <MapPin className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                View Floor Plan
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start text-sm h-9 sm:h-10">
              <Link to="/bookings">
                <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                View All Bookings
              </Link>
            </Button>
            <Button asChild variant="outline" className="w-full justify-start text-sm h-9 sm:h-10">
              <Link to="/profile">
                <User className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                Manage Profile
              </Link>
            </Button>

            <div className="pt-3 sm:pt-4 border-t">
              <h4 className="text-sm font-medium mb-2">Workspace Overview</h4>
              <div className="text-xs sm:text-sm text-muted-foreground space-y-1">
                <div className="flex justify-between">
                  <span>Available Desks:</span>
                  <span>{teamStats?.availableDesks || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Desks:</span>
                  <span>{teamStats?.totalDesks || 0}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Team Bookings */}
      <Card>
        <CardHeader className="pb-3 sm:pb-6">
          <CardTitle className="text-lg sm:text-xl">Team Bookings</CardTitle>
          <CardDescription className="text-sm">
            Recent and upcoming desk reservations from your team
          </CardDescription>
        </CardHeader>
        <CardContent>
          {bookingsLoading ? (
            <div className="flex items-center justify-center py-6 sm:py-8">
              <div className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 border-b-2 border-primary"></div>
            </div>
          ) : teamBookings && teamBookings.length > 0 ? (
            <>
              {/* Desktop Table View */}
              {!isMobile ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Team Member</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Time</TableHead>
                      <TableHead>Desk & Location</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {teamBookings.map((booking) => {
                      const status = getBookingStatus(booking);
                      return (
                        <TableRow key={booking.id}>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={booking.user_avatar || ''} alt={booking.user_name} />
                                <AvatarFallback className="text-xs">
                                  {booking.user_name?.charAt(0)?.toUpperCase() || 'U'}
                                </AvatarFallback>
                              </Avatar>
                              <span className="font-medium">{booking.user_name}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {formatDateRange(booking.date, booking.end_date)}
                          </TableCell>
                          <TableCell>
                            {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{booking.desk_name}</p>
                              <p className="text-sm text-muted-foreground">
                                {booking.zone_name} • Floor {booking.floor_number}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={status.variant}>
                              {status.label}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              ) : (
                /* Mobile Card View */
                <div className="space-y-3">
                  {teamBookings.map((booking) => {
                    const status = getBookingStatus(booking);
                    return (
                      <div key={booking.id} className="p-3 border rounded-lg space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Avatar className="h-7 w-7">
                              <AvatarImage src={booking.user_avatar || ''} alt={booking.user_name} />
                              <AvatarFallback className="text-xs">
                                {booking.user_name?.charAt(0)?.toUpperCase() || 'U'}
                              </AvatarFallback>
                            </Avatar>
                            <span className="font-medium text-sm">{booking.user_name}</span>
                          </div>
                          <Badge variant={status.variant} className="text-xs">
                            {status.label}
                          </Badge>
                        </div>

                        <div className="space-y-1 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Date:</span>
                            <span>{formatDateRange(booking.date, booking.end_date)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Time:</span>
                            <span>{formatTime(booking.start_time)} - {formatTime(booking.end_time)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Desk:</span>
                            <span className="font-medium">{booking.desk_name}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Location:</span>
                            <span>{booking.zone_name} • Floor {booking.floor_number}</span>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-6 sm:py-8 text-muted-foreground">
              <Calendar className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-3 sm:mb-4 opacity-50" />
              <p className="text-sm sm:text-base">No team bookings found</p>
              <p className="text-xs sm:text-sm">Your team hasn't made any recent bookings</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 