-- Absolute simplest SQL executor - just runs the SQL, no validation, no complexity

CREATE OR REPLACE FUNCTION exec(sql TEXT)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    result JSON;
BEGIN
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', sql) INTO result;
    RETURN COALESCE(result, '[]'::JSON);
END;
$$;

GRANT EXECUTE ON FUNCTION exec(TEXT) TO authenticated;

-- Test it
SELECT exec('SELECT 1 as test'); 