-- Check the specific booking that's showing "Unknown Desk"
SELECT 
  b.id,
  b.desk_id,
  b.date,
  b.end_date,
  b.status,
  b.user_id,
  d.name as desk_name,
  z.name as zone_name,
  z.floor_number
FROM bookings b
LEFT JOIN desks d ON b.desk_id = d.id
LEFT JOIN zones z ON d.zone_id = z.id
WHERE b.id = '2868707b-a7b3-4881-8ab6-e4a699149e30';

-- Also check if there are any bookings with null desk_id
SELECT 
  id,
  desk_id,
  date,
  end_date,
  status
FROM bookings 
WHERE desk_id IS NULL 
ORDER BY created_at DESC 
LIMIT 10;

-- Check total desk count
SELECT COUNT(*) as total_desks FROM desks;

-- Check if the desk_id exists in desks table
SELECT d.* FROM desks d 
WHERE d.id = (
  SELECT desk_id FROM bookings WHERE id = '2868707b-a7b3-4881-8ab6-e4a699149e30'
); 