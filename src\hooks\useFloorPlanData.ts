import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { DeskWithZone } from '@/types';
import { toast } from 'sonner';
import { startOfMonth, endOfMonth, startOfDay } from 'date-fns';
import { calculateRealtimeStatus } from '@/lib/utils';
import { generateICSFile, downloadICSFile } from '@/lib/calendar';

export function useFloorPlanData() {
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const { appUser } = useAuth();
  const queryClient = useQueryClient();

  // Fetch desks with zones
  const { data: desks, isLoading: isDesksLoading } = useQuery({
    queryKey: ['desks-with-zones'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('desks')
        .select(`
          *,
          zone:zones (*)
        `)
        .order('name');

      if (error) throw error;
      return data as DeskWithZone[];
    },
  });

  // Fetch user's current bookings
  const { data: userBookings } = useQuery({
    queryKey: ['user-current-bookings', appUser?.id],
    queryFn: async () => {
      if (!appUser?.id) return [];

      // Use local date formatting to avoid timezone issues
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const today = `${year}-${month}-${day}`;
      
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          desk_id,
          date,
          end_date,
          start_time,
          end_time,
          status,
          desks (
            name,
            zones (name, floor_number)
          )
        `)
        .eq('user_id', appUser.id)
        .gte('end_date', today)
        .in('status', ['booked', 'checked-in'])
        .order('date', { ascending: true });

      if (bookingsError) throw bookingsError;
      return bookingsData || [];
    },
    enabled: !!appUser?.id,
  });

  // Fetch upcoming bookings for all desks to show availability status
  const { data: upcomingBookings } = useQuery({
    queryKey: ['upcoming-bookings'],
    queryFn: async () => {
      const today = new Date();
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(today.getDate() + 3);
      
      // Use local date formatting to avoid timezone issues
      const formatDateToLocal = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      const todayStr = formatDateToLocal(today);
      const threeDaysFromNowStr = formatDateToLocal(threeDaysFromNow);
      
      // First get the bookings
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          user_id,
          desk_id,
          date,
          end_date,
          start_time,
          end_time,
          status
        `)
        .lte('date', threeDaysFromNowStr)
        .gte('end_date', todayStr)
        .in('status', ['booked', 'checked-in'])
        .order('date', { ascending: true });

      if (bookingsError) {
        console.error('Error fetching bookings:', bookingsError);
        return [];
      }

      if (!bookingsData || bookingsData.length === 0) {
        return [];
      }

      // Get unique user IDs
      const userIds = [...new Set(bookingsData.map(b => b.user_id))];
      
      // Fetch user names and avatars separately
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, name, avatar')
        .in('id', userIds);

      if (usersError) {
        console.error('Error fetching users:', usersError);
        // Return bookings without user names
        return bookingsData;
      }

      // Combine bookings with user data
      const data = bookingsData.map(booking => ({
        ...booking,
        users: usersData?.find(user => user.id === booking.user_id)
      })) as (typeof bookingsData[0] & { users?: { id: string; name: string; avatar?: string } })[];

      return data || [];
    },
  });

  // Fetch monthly bookings for calendar view
  const { data: monthlyBookings } = useQuery({
    queryKey: ['monthly-bookings', currentMonth.getFullYear(), currentMonth.getMonth()],
    queryFn: async () => {
      const monthStart = startOfMonth(currentMonth);
      const monthEnd = endOfMonth(currentMonth);
      
      // Use local date formatting to avoid timezone issues
      const formatDateToLocal = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };
      
      const monthStartStr = formatDateToLocal(monthStart);
      const monthEndStr = formatDateToLocal(monthEnd);
      
      const { data: bookingsData, error: bookingsError } = await supabase
        .from('bookings')
        .select(`
          id,
          user_id,
          desk_id,
          date,
          end_date,
          start_time,
          end_time,
          status,
          desk:desks (
            id,
            name,
            zone:zones (
              id,
              name,
              floor_number
            )
          )
        `)
        .lte('date', monthEndStr)
        .gte('end_date', monthStartStr)
        .in('status', ['booked', 'checked-in'])
        .order('date', { ascending: true });

      if (bookingsError) {
        console.error('Error fetching monthly bookings:', bookingsError);
        return [];
      }

      if (!bookingsData || bookingsData.length === 0) {
        return [];
      }

      // Get unique user IDs
      const userIds = [...new Set(bookingsData.map(b => b.user_id))];
      
      // Fetch user names and avatars separately
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, name, avatar')
        .in('id', userIds);

      if (usersError) {
        console.error('Error fetching users:', usersError);
        return bookingsData;
      }

      // Combine bookings with user data
      const data = bookingsData.map(booking => ({
        ...booking,
        users: usersData?.find(user => user.id === booking.user_id)
      })) as any[];

      return data || [];
    },
  });

  // Fetch existing bookings for a specific desk (used in booking dialog)
  const useExistingBookings = (selectedDeskId?: string, startDate?: Date, endDate?: Date) => {
    return useQuery({
      queryKey: ['desk-bookings', selectedDeskId, startDate, endDate],
      queryFn: async () => {
        if (!selectedDeskId || !startDate) return [];
        
        const searchEndDate = endDate || startDate;
        // Use local date formatting to avoid timezone issues
        const formatDateToLocal = (date: Date) => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');
          return `${year}-${month}-${day}`;
        };
        const startDateStr = formatDateToLocal(startDate);
        const endDateStr = formatDateToLocal(searchEndDate);
        
        const { data, error } = await supabase
          .from('bookings')
          .select('date, end_date, start_time, end_time, status')
          .eq('desk_id', selectedDeskId)
          .lte('date', endDateStr)
          .gte('end_date', startDateStr)
          .in('status', ['booked', 'checked-in']);

        if (error) throw error;
        return data || [];
      },
      enabled: !!selectedDeskId && !!startDate,
    });
  };

  // Mutation to create a booking
  const createBookingMutation = useMutation({
    mutationFn: async ({ 
      deskId, 
      startDate, 
      endDate, 
      startTime, 
      endTime,
      downloadCalendar = false
    }: { 
      deskId: string; 
      startDate: string; 
      endDate: string; 
      startTime: string; 
      endTime: string;
      downloadCalendar?: boolean;
    }) => {
      const { error } = await supabase
        .from('bookings')
        .insert({
          user_id: appUser!.id,
          desk_id: deskId,
          date: startDate,
          end_date: endDate,
          start_time: startTime,
          end_time: endTime,
          status: 'booked'
        });
      
      if (error) throw error;
      return { downloadCalendar };
    },
    onSuccess: (_, variables) => {
      // Only download calendar file if user opted in
      if (variables.downloadCalendar) {
        const selectedDesk = desks?.find(d => d.id === variables.deskId);
        
        if (selectedDesk) {
          const icsContent = generateICSFile({
            deskName: selectedDesk.name,
            zoneName: selectedDesk.zone.name,
            startDate: variables.startDate,
            endDate: variables.endDate,
            startTime: variables.startTime,
            endTime: variables.endTime,
            userName: appUser?.name || 'You'
          });
          
          const filename = `desk-booking-${variables.startDate}.ics`;
          downloadICSFile(icsContent, filename);
        }
      }
      
      queryClient.invalidateQueries({ queryKey: ['desk-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
      
      const message = variables.downloadCalendar 
        ? 'Desk booked successfully! Calendar event downloaded.'
        : 'Desk booked successfully!';
      toast.success(message);
    },
    onError: (error) => {
      toast.error('Failed to book desk: ' + error.message);
    },
  });

  // Mutation to delete a booking
  const deleteBookingMutation = useMutation({
    mutationFn: async (bookingId: string) => {
      const { error } = await supabase
        .from('bookings')
        .delete()
        .eq('id', bookingId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['desk-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-current-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['dashboard-stats'] });
      queryClient.invalidateQueries({ queryKey: ['upcoming-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['monthly-bookings'] });
      toast.success('Booking cancelled successfully!');
    },
    onError: (error) => {
      toast.error('Failed to cancel booking: ' + error.message);
    },
  });

  // Helper functions
  const getNextBookingsForDesk = (deskId: string) => {
    if (!upcomingBookings) return [];
    
    return upcomingBookings
      .filter(booking => booking.desk_id === deskId)
      .slice(0, 3) as (typeof upcomingBookings[0] & { users?: { id: string; name: string; avatar?: string } })[];
  };

  const getNextBookingsForDeskFromDate = (deskId: string, fromDate: Date) => {
    if (!upcomingBookings) return [];
    
    const fromDateStart = startOfDay(fromDate);
    
    return upcomingBookings
      .filter(booking => {
        const bookingDate = new Date(booking.date);
        return booking.desk_id === deskId && bookingDate >= fromDateStart;
      })
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
      .slice(0, 3) as (typeof upcomingBookings[0] & { users?: { id: string; name: string; avatar?: string } })[];
  };

  const hasSoonBookings = (deskId: string) => {
    if (!upcomingBookings) return false;
    
    const today = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(today.getDate() + 3);
    
    return upcomingBookings.some(booking => 
      booking.desk_id === deskId &&
      new Date(booking.date) <= threeDaysFromNow
    );
  };

  const getCurrentBooking = (deskId: string) => {
    if (!upcomingBookings) return null;

    const now = new Date();
    // Use local date formatting to avoid timezone issues
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const today = `${year}-${month}-${day}`;
    const currentTime = now.toTimeString().slice(0, 5); // HH:MM format

    return upcomingBookings.find(booking => {
      if (booking.desk_id !== deskId) return false;
      
      const bookingStartDate = booking.date;
      const bookingEndDate = booking.end_date;
      
      const isWithinDateRange = today >= bookingStartDate && today <= bookingEndDate;
      if (!isWithinDateRange) return false;

      if (bookingStartDate !== bookingEndDate) return true;

      return currentTime >= booking.start_time && currentTime <= booking.end_time;
    }) as (typeof upcomingBookings[0] & { users?: { id: string; name: string; avatar?: string } }) | undefined;
  };

  // Calculate real-time desk status based on current bookings
  const getRealtimeStatus = (desk: DeskWithZone) => {
    return calculateRealtimeStatus(
      desk.status,
      desk.id,
      upcomingBookings || []
    );
  };

  // Calendar helper functions
  const getBookingsForDay = (date: Date) => {
    if (!monthlyBookings) return [];
    
    // Use local date string instead of UTC to avoid timezone issues
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const dayString = `${year}-${month}-${day}`;
    
    return monthlyBookings.filter(booking => {
      const bookingStart = booking.date;
      const bookingEnd = booking.end_date;
      return dayString >= bookingStart && dayString <= bookingEnd;
    });
  };

  const getBookingCountForDay = (date: Date) => {
    return getBookingsForDay(date).length;
  };

  const getAvailableDesksForDay = (date: Date) => {
    if (!desks) return [];
    
    const dayBookings = getBookingsForDay(date);
    const bookedDeskIds = new Set(dayBookings.map(booking => booking.desk_id));
    
    return desks.filter(desk => !bookedDeskIds.has(desk.id));
  };

  const getAvailableCountForDay = (date: Date) => {
    return getAvailableDesksForDay(date).length;
  };

  const getTotalDeskCount = () => {
    return desks?.length || 0;
  };

  // Group desks by floor and zone
  const groupedData = desks?.reduce((acc, desk) => {
    const floorNumber = desk.zone.floor_number;
    const zoneId = desk.zone.id;
    
    if (!acc[floorNumber]) {
      acc[floorNumber] = {};
    }
    
    if (!acc[floorNumber][zoneId]) {
      acc[floorNumber][zoneId] = {
        zone: desk.zone,
        desks: []
      };
    }
    
    acc[floorNumber][zoneId].desks.push(desk);
    return acc;
  }, {} as Record<number, Record<string, { zone: any; desks: DeskWithZone[] }>>) || {};

  // Get available floors
  const availableFloors = Object.keys(groupedData).map(Number).sort((a, b) => a - b);

  return {
    // Data
    desks,
    userBookings,
    upcomingBookings,
    monthlyBookings,
    groupedData,
    availableFloors,
    
    // Loading states
    isLoading: isDesksLoading,
    
    // Calendar state
    currentMonth,
    setCurrentMonth,
    
    // Custom hooks
    useExistingBookings,
    
    // Mutations
    createBookingMutation,
    deleteBookingMutation,
    
    // Helper functions
    getNextBookingsForDesk,
    getNextBookingsForDeskFromDate,
    hasSoonBookings,
    getCurrentBooking,
    getRealtimeStatus,
    getBookingsForDay,
    getBookingCountForDay,
    getAvailableDesksForDay,
    getAvailableCountForDay,
    getTotalDeskCount,
  };
} 