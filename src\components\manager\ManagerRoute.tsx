import { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';

interface ManagerRouteProps {
  children: ReactNode;
}

export function ManagerRoute({ children }: ManagerRouteProps) {
  const { appUser, loading } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!appUser) {
    return <Navigate to="/auth" replace />;
  }

  if (appUser.role !== 'manager' && appUser.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold">Access Denied</h2>
          <p className="text-muted-foreground">Manager access required</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 