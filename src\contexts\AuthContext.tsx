import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import { User as AppUser } from '@/types';

interface AuthContextType {
  user: User | null;
  appUser: AppUser | null;
  session: Session | null;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string, managerId?: string | null) => Promise<void>;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [appUser, setAppUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchAppUser(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        fetchAppUser(session.user.id);
      } else {
        setAppUser(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const fetchAppUser = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;
      setAppUser(data);
    } catch (error) {
      console.error('Error fetching app user:', error);
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    // Check if email domain is allowed
    if (!email.endsWith('@symplexity.co.za')) {
      throw new Error('Only @symplexity.co.za email addresses are allowed');
    }

    console.log('Attempting to sign in with:', { email });

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Supabase signIn error:', error);
      
      // Handle specific error codes
      if (error.message === 'Invalid login credentials') {
        throw new Error('Invalid email or password. Please check your credentials and try again.');
      }
      if (error.message === 'Email not confirmed') {
        throw new Error('Please check your email and click the confirmation link before signing in.');
      }
      if (error.message === 'Too many requests') {
        throw new Error('Too many login attempts. Please wait a few minutes and try again.');
      }
      
      throw new Error(`Login failed: ${error.message}`);
    }

    console.log('Sign in successful:', data);
  };

  const signUp = async (email: string, password: string, name: string, managerId?: string | null) => {
    // Check if email domain is allowed
    if (!email.endsWith('@symplexity.co.za')) {
      throw new Error('Only @symplexity.co.za email addresses are allowed');
    }

    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name,
            role: 'employee',
            manager_id: managerId === 'none' ? null : managerId
          },
          emailRedirectTo: window.location.origin
        }
      });

      if (error) {
        console.error('Supabase signup error:', error);
        
        // Handle specific error codes
        if (error.message === 'User already registered') {
          throw new Error('An account with this email already exists. Please try signing in instead.');
        }
        if (error.message === 'Signups not allowed for this instance') {
          throw new Error('Account creation is currently disabled. Please contact an administrator.');
        }
        if (error.message === 'Email not confirmed') {
          throw new Error('Please check your email and click the confirmation link before signing in.');
        }
        
        throw error;
      }

      if (data.user && !data.user.email_confirmed_at) {
        // If email confirmation is required, don't create the user record yet
        // It will be created via trigger after email confirmation
        return;
      }

      if (data.user) {
        // Create app user record only if user is confirmed or email confirmation is disabled
        const { error: userError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email,
            name,
            role: 'employee',
            manager_id: managerId === 'none' ? null : managerId,
          });

        if (userError) {
          console.error('Error creating user record:', userError);
          // Don't throw here as the auth user was created successfully
          // The user record will be created later via trigger
        }
      }
    } catch (error: any) {
      console.error('SignUp error:', error);
      throw error;
    }
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const value = {
    user,
    appUser,
    session,
    loading,
    signIn,
    signUp,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}