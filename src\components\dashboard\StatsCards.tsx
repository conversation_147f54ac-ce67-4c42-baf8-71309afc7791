import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Building2, Calendar, CheckCircle, Clock } from 'lucide-react';

interface StatsCardsProps {
  totalDesks: number;
  availableDesks: number;
  myBookings: number;
  upcomingBookings: number;
}

export function StatsCards({ 
  totalDesks, 
  availableDesks, 
  myBookings, 
  upcomingBookings 
}: StatsCardsProps) {
  const stats = [
    {
      title: 'Total Desks',
      value: totalDesks,
      icon: Building2,
      description: 'Available workspace',
    },
    {
      title: 'Available Now',
      value: availableDesks,
      icon: CheckCircle,
      description: 'Ready to book',
    },
    {
      title: 'My Bookings',
      value: myBookings,
      icon: Calendar,
      description: 'Total reservations',
    },
    {
      title: 'Upcoming',
      value: upcomingBookings,
      icon: Clock,
      description: 'This week',
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat, index) => (
        <Card key={index} className="shadow-clay-sm hover:shadow-clay-md transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">{stat.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}