-- ===================================
-- DROP PROBLEMATIC RLS POLICIES
-- Remove table RLS that's blocking manager/admin access
-- Keep storage policies for file uploads
-- ===================================

-- 1. Drop all restrictive bookings policies
DROP POLICY IF EXISTS "bookings_delete_own" ON bookings;
DROP POLICY IF EXISTS "bookings_insert_own" ON bookings;
DROP POLICY IF EXISTS "bookings_select_own" ON bookings;
DROP POLICY IF EXISTS "bookings_update_own" ON bookings;

-- 2. Drop desk policies (they have admin-only checks that might be problematic)
DROP POLICY IF EXISTS "Admins can manage desks" ON desks;
DROP POLICY IF EXISTS "Authenticated users can read desks" ON desks;

-- 3. Drop zone policies
DROP POLICY IF EXISTS "Admins can manage zones" ON zones;
DROP POLICY IF EXISTS "Authenticated users can read zones" ON zones;

-- 4. Drop problematic user policies
DROP POLICY IF EXISTS "Users can update their manager during initial setup" ON users;
DROP POLICY IF EXISTS "users_own_insert" ON users;

-- 5. Drop settings policies
DROP POLICY IF EXISTS "Admins can manage settings" ON settings;
DROP POLICY IF EXISTS "Authenticated users can read settings" ON settings;

-- 6. Disable RLS on main tables (keep storage RLS for security)
ALTER TABLE bookings DISABLE ROW LEVEL SECURITY;
ALTER TABLE desks DISABLE ROW LEVEL SECURITY;
ALTER TABLE zones DISABLE ROW LEVEL SECURITY;
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE settings DISABLE ROW LEVEL SECURITY;

-- NOTE: We're keeping storage.objects RLS policies intact for security:
-- - avatars_authenticated_delete
-- - avatars_authenticated_update  
-- - avatars_authenticated_upload
-- - avatars_public_read
-- - zone_photos_auth_delete
-- - zone_photos_auth_update
-- - zone_photos_auth_upload
-- - zone_photos_public_read

-- 7. Verify the changes
SELECT 
  'RLS POLICIES REMOVED' as status,
  'Table RLS disabled, Storage RLS preserved' as message,
  NOW() as applied_at;

-- 8. Show remaining policies (should only be storage policies)
SELECT 
  schemaname,
  tablename,
  policyname,
  cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- 9. Show tables with RLS still enabled (should be empty for public schema)
SELECT 
  tablename,
  rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND rowsecurity = true; 