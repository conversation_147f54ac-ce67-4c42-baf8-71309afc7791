import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/lib/supabase';
import { StatsCards } from '@/components/dashboard/StatsCards';
import { RecentBookings } from '@/components/dashboard/RecentBookings';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Separator } from '@/components/ui/separator';
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbList, 
  BreadcrumbPage 
} from '@/components/ui/breadcrumb';
import { CalendarDays, MapPin } from 'lucide-react';
import { Link } from 'react-router-dom';

export function Dashboard() {
  const { appUser } = useAuth();

  const { data: stats, isLoading: statsLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      const [desksRes, myBookingsRes, allBookingsRes] = await Promise.all([
        supabase.from('desks').select('id, status'),
        supabase
          .from('bookings')
          .select('*')
          .eq('user_id', appUser?.id)
          .gte('date', new Date().toISOString().split('T')[0]),
        supabase
          .from('bookings')
          .select('desk_id, date, end_date, start_time, end_time, status')
          .gte('end_date', new Date().toISOString().split('T')[0])
          .in('status', ['booked', 'checked-in'])
      ]);

      const desks = desksRes.data || [];
      const allBookings = allBookingsRes.data || [];
      
      // Calculate real-time availability using the utility function
      const { calculateRealtimeStatus } = await import('@/lib/utils');
      const availableDesks = desks.filter(desk => 
        calculateRealtimeStatus(desk.status, desk.id, allBookings) === 'available'
      ).length;

      const totalDesks = desks.length;
      const myBookings = myBookingsRes.data?.length || 0;
      const upcomingBookings = myBookingsRes.data?.filter(b => b.status === 'booked').length || 0;

      return {
        totalDesks,
        availableDesks,
        myBookings,
        upcomingBookings,
      };
    },
    enabled: !!appUser?.id,
  });

  const { data: recentBookings } = useQuery({
    queryKey: ['recent-bookings', appUser?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('bookings')
        .select(`
          *,
          desk:desks (
            *,
            zone:zones (*)
          )
        `)
        .eq('user_id', appUser?.id)
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;
      return data || [];
    },
    enabled: !!appUser?.id,
  });

  return (
    <>
      <header className="flex h-16 shrink-0 items-center gap-2">
        <div className="flex items-center gap-2 px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbPage>Dashboard</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
      </header>

      <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
        <div className="grid auto-rows-min gap-4 md:grid-cols-3">
          <div className="md:col-span-3">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Welcome back, {appUser?.name}!</h1>
                <p className="text-muted-foreground">
                  Here's an overview of your workspace activity
                </p>
              </div>
              <div className="flex gap-2">
                <Button asChild>
                  <Link to="/floor-plan">
                    <MapPin className="h-4 w-4 mr-2" />
                    View Floor Plan
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/bookings">
                    <CalendarDays className="h-4 w-4 mr-2" />
                    My Bookings
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </div>

        {statsLoading ? (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="shadow-clay-sm">
                <CardHeader className="pb-2">
                  <div className="h-4 bg-muted animate-pulse rounded" />
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-muted animate-pulse rounded mb-2" />
                  <div className="h-3 bg-muted animate-pulse rounded" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <StatsCards
            totalDesks={stats?.totalDesks || 0}
            availableDesks={stats?.availableDesks || 0}
            myBookings={stats?.myBookings || 0}
            upcomingBookings={stats?.upcomingBookings || 0}
          />
        )}

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <RecentBookings bookings={recentBookings || []} />
          
          <Card className="shadow-clay-sm">
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild className="w-full justify-start">
                <Link to="/floor-plan">
                  <MapPin className="h-4 w-4 mr-2" />
                  Book a Desk
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link to="/bookings">
                  <CalendarDays className="h-4 w-4 mr-2" />
                  View My Bookings
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
}