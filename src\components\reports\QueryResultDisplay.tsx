import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { Download, BarChart3, TrendingUp, Users, Building2, Calendar, Hash } from 'lucide-react';
import { toast } from 'sonner';

interface QueryResult {
  query?: string;
  sql?: string;
  results?: any[];
  data?: any[];
  type?: 'table' | 'chart' | 'metric';
  visualization?: 'table' | 'chart' | 'metric';
  title?: string;
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  columns?: string[];
  summary?: string;
  explanation?: string;
  loading?: boolean;
  error?: string;
}

interface QueryResultDisplayProps {
  result: QueryResult;
  onExport?: () => void;
}

const CHART_COLORS = [
  '#8B5CF6', '#06B6D4', '#10B981', '#F59E0B', '#EF4444', '#8B5A2B', '#EC4899', '#6366F1'
];

const chartConfig = {
  value: {
    label: 'Value',
    color: 'hsl(var(--chart-1))',
  },
  count: {
    label: 'Count',
    color: 'hsl(var(--chart-2))',
  },
};

export function QueryResultDisplay({ result, onExport }: QueryResultDisplayProps) {
  if (result.error) {
    return (
      <Card className="border-destructive/20 bg-destructive/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Hash className="h-5 w-5" />
            Query Error
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-destructive">{result.error}</p>
        </CardContent>
      </Card>
    );
  }

  // Handle both 'results' and 'data' properties for backward compatibility
  const queryData = result.results || result.data || [];
  
  if (!queryData || queryData.length === 0) {
    return (
      <Card className="border-muted">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            No Results
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Your query executed successfully but returned no results. Try adjusting your question or date range.
          </p>
        </CardContent>
      </Card>
    );
  }

  const exportData = () => {
    if (onExport) {
      onExport();
    } else {
      // Default export functionality
      const csvContent = convertToCSV(queryData);
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `query-results-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Results exported successfully!');
    }
  };

  const convertToCSV = (data: any[]): string => {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header];
          return typeof value === 'string' ? `"${value}"` : value;
        }).join(',')
      )
    ];
    
    return csvRows.join('\n');
  };

  const renderVisualization = () => {
    const visualizationType = result.visualization || result.type || 'table';
    switch (visualizationType) {
      case 'metric':
        return renderMetricView();
      case 'chart':
        return renderChartView();
      case 'table':
      default:
        return renderTableView();
    }
  };

  const renderMetricView = () => {
    // For single metric results
    if (queryData.length === 1 && Object.keys(queryData[0]).length <= 2) {
      const data = queryData[0];
      const keys = Object.keys(data);
      const valueKey = keys.find(k => typeof data[k] === 'number') || keys[0];
      const labelKey = keys.find(k => k !== valueKey) || keys[0];
      
      return (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {labelKey ? data[labelKey] : 'Result'}
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{data[valueKey]}</div>
              <p className="text-xs text-muted-foreground">
                Based on your query
              </p>
            </CardContent>
          </Card>
        </div>
      );
    }
    
    // For multiple metrics
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {queryData.slice(0, 6).map((item, index) => {
          const keys = Object.keys(item);
          const valueKey = keys.find(k => typeof item[k] === 'number') || keys[0];
          const labelKey = keys.find(k => k !== valueKey) || keys[0];
          
          return (
            <Card key={index}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  {labelKey ? item[labelKey] : `Item ${index + 1}`}
                </CardTitle>
                <Hash className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{item[valueKey]}</div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    );
  };

  const renderChartView = () => {
    if (queryData.length === 0) return null;
    
    const data = queryData;
    const keys = Object.keys(data[0]);
    const xKey = keys[0];
    const yKey = keys.find(k => typeof data[0][k] === 'number') || keys[1];
    
    // Determine chart type based on data characteristics
    const isTimeSeriesData = keys.some(k => k.includes('date') || k.includes('time'));
    const hasMultipleNumericFields = keys.filter(k => typeof data[0][k] === 'number').length > 1;
    
    if (isTimeSeriesData) {
      return (
        <ChartContainer config={chartConfig} className="h-80">
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xKey} />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Line
              type="monotone"
              dataKey={yKey}
              stroke={CHART_COLORS[0]}
              strokeWidth={2}
              dot={{ r: 4 }}
            />
          </LineChart>
        </ChartContainer>
      );
    }
    
    if (data.length <= 10) {
      return (
        <ChartContainer config={chartConfig} className="h-80">
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey={xKey} />
            <YAxis />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey={yKey} radius={[4, 4, 0, 0]}>
              {data.map((_, index) => (
                <Cell key={`cell-${index}`} fill={CHART_COLORS[index % CHART_COLORS.length]} />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      );
    }
    
    return renderTableView();
  };

  const renderTableView = () => {
    const data = queryData;
    const headers = Object.keys(data[0]);
    
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {headers.map((header) => (
                <TableHead key={header} className="capitalize">
                  {header.replace(/_/g, ' ')}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.map((row, index) => (
              <TableRow key={index}>
                {headers.map((header) => (
                  <TableCell key={header}>
                    {formatCellValue(row[header])}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  };

  const formatCellValue = (value: any): React.ReactNode => {
    if (value === null || value === undefined) {
      return <span className="text-muted-foreground">-</span>;
    }
    
    if (typeof value === 'boolean') {
      return <Badge variant={value ? 'default' : 'secondary'}>{value ? 'Yes' : 'No'}</Badge>;
    }
    
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    
    if (typeof value === 'string' && value.includes('@')) {
      return <span className="font-mono text-sm">{value}</span>;
    }
    
    return value;
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Query Results
              <Badge variant="outline" className="ml-2">
                {queryData.length} {queryData.length === 1 ? 'result' : 'results'}
              </Badge>
            </CardTitle>
            <CardDescription className="mt-2">
              {result.query || result.title || 'Query Results'}
            </CardDescription>
          </div>
          <Button variant="outline" size="sm" onClick={exportData}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {renderVisualization()}
      </CardContent>
    </Card>
  );
} 