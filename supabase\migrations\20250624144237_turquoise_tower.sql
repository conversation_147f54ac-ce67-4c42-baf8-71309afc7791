/*
  # Fix Users RLS Policy Infinite Recursion

  1. Problem
    - The "Admin users can read all data" policy creates infinite recursion by querying the users table from within a policy that governs the users table
    
  2. Solution
    - Drop the problematic admin policy
    - Create a simpler approach that doesn't cause recursion
    - Keep the working user policy for reading own data
    
  3. Security
    - Users can still read their own data
    - Admin functionality will need to be handled differently (e.g., through service role or different approach)
*/

-- Drop the problematic admin policy that causes infinite recursion
DROP POLICY IF EXISTS "Admin users can read all data" ON users;

-- The existing "Users can read own data" policy remains and works correctly
-- (uid() = id) - this doesn't cause recursion as it doesn't query the users table

-- For admin functionality, we'll need to handle this through the application layer
-- or use a different approach that doesn't create circular references