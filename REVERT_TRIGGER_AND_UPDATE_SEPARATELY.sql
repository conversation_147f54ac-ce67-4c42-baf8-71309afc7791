/*
  # Revert trigger and handle manager assignment separately

  1. Problem
    - The updated trigger is causing database errors during signup
    - Need to get basic signup working first

  2. Solution
    - Revert trigger to original working state
    - Handle manager assignment as a separate update after user creation

  3. Approach
    - User signs up successfully without manager assignment
    - Frontend updates the user record separately with manager_id
*/

-- Revert to original working trigger function
CREATE OR REPLACE FUNCTION public.handle_new_user() 
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, role)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'employee')
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Make sure users can update their own manager_id during initial setup
DROP POLICY IF EXISTS "Users can update their manager during initial setup" ON users;
CREATE POLICY "Users can update their manager during initial setup"
  ON users
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id); 