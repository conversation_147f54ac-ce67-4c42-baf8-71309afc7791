import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { format, eachDayOfInterval, startOfMonth, endOfMonth, isSameMonth, isToday, addMonths, subMonths, startOfWeek, endOfWeek } from 'date-fns';
import { cn } from '@/lib/utils';

interface CalendarViewProps {
  currentMonth: Date;
  setCurrentMonth: (date: Date) => void;
  monthlyBookings: any[] | undefined;
  getBookingCountForDay: (date: Date) => number;
  getAvailableCountForDay: (date: Date) => number;
  getTotalDeskCount: () => number;
  handleDayClick: (date: Date) => void;
  isDateDisabled: (date: Date) => boolean;
}

export function CalendarView({
  currentMonth,
  setCurrentMonth,
  monthlyBookings,
  getBookingCountForDay,
  getAvailableCountForDay,
  getTotalDeskCount,
  handleDayClick,
  isDateDisabled,
}: CalendarViewProps) {
  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(direction === 'next' ? addMonths(currentMonth, 1) : subMonths(currentMonth, 1));
  };

  return (
    <div className="space-y-4 md:space-y-6">
      {/* Calendar Header */}
      <Card className="shadow-clay-sm">
        <CardHeader className="pb-3 md:pb-4">
          <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
            <div>
              <CardTitle className="text-lg md:text-xl">
                {format(currentMonth, 'MMMM yyyy')}
              </CardTitle>
              <p className="text-xs md:text-sm text-muted-foreground mt-1">
                {monthlyBookings ? monthlyBookings.length : 0} total bookings this month
              </p>
            </div>
            <div className="flex items-center justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('prev')}
                className="h-8 w-8 p-0 md:h-10 md:w-auto md:px-3"
              >
                <ChevronLeft className="h-4 w-4" />
                <span className="hidden md:inline ml-1">Previous</span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentMonth(new Date())}
                className="h-8 px-3 md:h-10"
              >
                Today
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateMonth('next')}
                className="h-8 w-8 p-0 md:h-10 md:w-auto md:px-3"
              >
                <ChevronRight className="h-4 w-4" />
                <span className="hidden md:inline ml-1">Next</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-3 md:px-6">
          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 md:gap-2">
            {/* Day Headers */}
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="p-1 md:p-2 text-center text-xs md:text-sm font-medium text-muted-foreground">
                <span className="hidden sm:inline">{day}</span>
                <span className="sm:hidden">{day.charAt(0)}</span>
              </div>
            ))}
            
            {/* Calendar Days */}
            {eachDayOfInterval({
              start: startOfWeek(startOfMonth(currentMonth)),
              end: endOfWeek(endOfMonth(currentMonth))
            }).map((date) => {
              const bookingCount = getBookingCountForDay(date);
              const availableCount = getAvailableCountForDay(date);
              const totalDesks = getTotalDeskCount();
              const isCurrentDay = isToday(date);
              const isDisabled = isDateDisabled(date);
              
              return (
                <button
                  key={date.toISOString()}
                  onClick={() => !isDisabled && handleDayClick(date)}
                  disabled={isDisabled}
                  className={cn(
                    "p-1 md:p-3 min-h-[60px] md:min-h-[100px] text-left border rounded-md md:rounded-lg transition-colors flex flex-col",
                    !isDisabled && "hover:bg-muted/50 cursor-pointer",
                    isDisabled && "cursor-not-allowed opacity-50 bg-muted/20",
                    isCurrentDay && !isDisabled && "bg-primary/10 border-primary",
                    !isSameMonth(date, currentMonth) && "opacity-30",
                    bookingCount > 0 && !isDisabled && "border-blue-300 bg-blue-50 dark:bg-blue-950/20"
                  )}
                >
                  <div className="flex flex-col h-full justify-between min-h-0">
                    <span className={cn(
                      "text-xs md:text-sm font-medium mb-1",
                      isCurrentDay && "text-primary"
                    )}>
                      {format(date, 'd')}
                    </span>
                    <div className="space-y-0.5 md:space-y-1 flex-1 flex flex-col justify-end">
                      {bookingCount > 0 && (
                        <Badge variant="secondary" className="text-xs px-1 py-0 h-4 md:h-5 text-center leading-none">
                          <span className="hidden sm:inline">{bookingCount} booked</span>
                          <span className="sm:hidden">{bookingCount}</span>
                        </Badge>
                      )}
                      {totalDesks > 0 && (
                        <Badge variant="outline" className={cn(
                          "text-xs px-1 py-0 h-4 md:h-5 text-center leading-none",
                          availableCount === 0 ? "text-red-600 border-red-300" : "text-green-600 border-green-300"
                        )}>
                          <span className="hidden sm:inline">{availableCount} available</span>
                          <span className="sm:hidden">{availableCount}</span>
                        </Badge>
                      )}
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 