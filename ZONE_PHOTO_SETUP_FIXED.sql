-- ===================================
-- ZONE PHOTO SETUP SQL (FIXED)
-- Run this in your Supabase SQL Editor
-- ===================================

-- Add photo field to zones table
ALTER TABLE zones ADD COLUMN IF NOT EXISTS photo TEXT;
ALTER TABLE zones ADD COLUMN IF NOT EXISTS description TEXT;

-- Create zone-photos storage bucket
INSERT INTO storage.buckets (id, name, public) 
VALUES ('zone-photos', 'zone-photos', true)
ON CONFLICT (id) DO NOTHING;

-- Update bucket to be public (this should work without special permissions)
UPDATE storage.buckets 
SET public = true 
WHERE id = 'zone-photos';

-- Note: Storage policies may need to be set up through the Supabase Dashboard
-- Go to Storage > zone-photos bucket > Policies tab and create these policies:
-- 
-- 1. Policy Name: "Public Zone Photo Access"
--    Operation: SELECT
--    Target: public
--    Policy: (no restrictions - allow all)
--
-- 2. Policy Name: "Authenticated Zone Photo Upload" 
--    Operation: INSERT
--    Target: authenticated
--    Policy: (no restrictions - allow all authenticated users)
--
-- 3. Policy Name: "Authenticated Zone Photo Update"
--    Operation: UPDATE  
--    Target: authenticated
--    Policy: (no restrictions - allow all authenticated users)
--
-- 4. Policy Name: "Authenticated Zone Photo Delete"
--    Operation: DELETE
--    Target: authenticated  
--    Policy: (no restrictions - allow all authenticated users)

-- Verify the changes
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'zones' 
AND column_name IN ('photo', 'description')
ORDER BY ordinal_position;

-- Verify bucket creation
SELECT 
  'zone-photos bucket created' as status,
  public as is_public
FROM storage.buckets 
WHERE id = 'zone-photos'; 