import { Database } from './database';

export type User = Database['public']['Tables']['users']['Row'];
export type Zone = Database['public']['Tables']['zones']['Row'];
export type Desk = Database['public']['Tables']['desks']['Row'];
export type Booking = Database['public']['Tables']['bookings']['Row'];
export type Settings = Database['public']['Tables']['settings']['Row'];

export interface DeskWithZone extends Desk {
  zone: Zone;
}

export interface BookingWithDetails extends Booking {
  desk: DeskWithZone;
  user: User;
}

export interface BookingWithUser extends Booking {
  users?: User;
}

export interface FloorPlan {
  id: string;
  name: string;
  floor_number: number;
  desks: DeskWithZone[];
}

export type DeskStatus = 'available' | 'occupied' | 'maintenance';
export type BookingStatus = 'booked' | 'checked-in' | 'cancelled';
export type UserRole = 'employee' | 'admin';