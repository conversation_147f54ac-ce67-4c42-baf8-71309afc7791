import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MapPin, User, CalendarDays, Calendar as CalendarIcon } from 'lucide-react';
import { DeskWithZone } from '@/types';
import { format } from 'date-fns';

interface DeskDetailsModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDesk: DeskWithZone | null;
  selectedDate: Date;
  getStatusForDate: (desk: DeskWithZone, date: Date) => string;
  getBookingForDate: (desk: DeskWithZone, date: Date) => any;
  getStatusBadgeColor: (status: string) => string;
  getNextBookingsForDesk: (deskId: string) => any[];
  formatTime: (timeString: string) => string;
  formatDateRange: (startDate: string, endDate: string) => string;
  onBookDesk: () => void;
}

export function DeskDetailsModal({
  isOpen,
  onOpenChange,
  selectedDesk,
  selectedDate,
  getStatusForDate,
  getBookingForDate,
  getStatusBadgeColor,
  getNextBookingsForDesk,
  formatTime,
  formatDateRange,
  onBookDesk,
}: DeskDetailsModalProps) {
  if (!selectedDesk) return null;

  // Use status for selected date instead of real-time status
  const statusForSelectedDate = getStatusForDate(selectedDesk, selectedDate);
  const bookingForSelectedDate = getBookingForDate(selectedDesk, selectedDate);
  
  // Get next bookings from the selected date instead of from today
  const allBookingsForDesk = getNextBookingsForDesk(selectedDesk.id);
  const nextBookings = allBookingsForDesk.filter(booking => {
    const bookingDate = new Date(booking.date);
    const selectedDateStart = new Date(selectedDate);
    selectedDateStart.setHours(0, 0, 0, 0); // Start of selected day
    return bookingDate >= selectedDateStart;
  }).slice(0, 3);

  // Format the selected date for display - shorter format

  const shortDateStr = format(selectedDate, 'MMM dd, yyyy'); // "Jul 02, 2025"

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-[520px] mx-auto max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-base md:text-lg">
            <MapPin className="h-4 w-4 md:h-5 md:w-5" />
            {selectedDesk.name}
          </DialogTitle>
          <DialogDescription className="text-sm">
            {selectedDesk.zone.name} • Floor {selectedDesk.zone.floor_number} • {shortDateStr}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 px-1">
          <div>
            <p className="text-sm font-medium mb-2">Status for {shortDateStr}</p>
            <div className="flex items-center gap-2">
              <Badge className={getStatusBadgeColor(statusForSelectedDate)}>
                {statusForSelectedDate}
              </Badge>
            </div>
          </div>

          {/* Show booking details if desk is occupied on selected date */}
          {statusForSelectedDate === 'occupied' && bookingForSelectedDate && (
            <div>
              <p className="text-sm font-medium mb-2 flex items-center gap-2">
                <User className="h-4 w-4" />
                Occupied on {shortDateStr}
              </p>
              <div className="flex items-center gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <Avatar className="h-8 w-8 md:h-10 md:w-10 flex-shrink-0">
                  <AvatarImage src={bookingForSelectedDate.users?.avatar || ''} alt={bookingForSelectedDate.users?.name} />
                  <AvatarFallback className="bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 text-xs">
                    {bookingForSelectedDate.users?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm truncate">{bookingForSelectedDate.users?.name || 'Unknown User'}</p>
                  <p className="text-xs text-muted-foreground break-words">
                    {formatDateRange(bookingForSelectedDate.date, bookingForSelectedDate.end_date)} • {formatTime(bookingForSelectedDate.start_time)} - {formatTime(bookingForSelectedDate.end_time)}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Show next 3 bookings for available desks */}
          {statusForSelectedDate === 'available' && (
            <div>
              <p className="text-sm font-medium mb-2 flex items-center gap-2">
                <CalendarDays className="h-4 w-4" />
                Next 3 Bookings
              </p>
              <div className="space-y-2">
                {nextBookings.length > 0 ? (
                  nextBookings.map((booking) => (
                    <div key={booking.id} className="p-3 bg-muted/30 rounded-md text-xs">
                      <div className="flex flex-col gap-2 md:flex-row md:items-center md:justify-between">
                        <span className="font-medium text-sm">
                          {formatDateRange(booking.date, booking.end_date)}
                        </span>
                        <span className="text-muted-foreground text-xs">
                          {formatTime(booking.start_time)} - {formatTime(booking.end_time)}
                        </span>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Avatar className="h-5 w-5 md:h-6 md:w-6">
                          <AvatarImage src={booking.users?.avatar || ''} alt={booking.users?.name} />
                          <AvatarFallback className="text-xs bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                            {booking.users?.name?.charAt(0)?.toUpperCase() || 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-muted-foreground text-xs truncate">{booking.users?.name || 'Unknown User'}</span>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-xs text-muted-foreground bg-green-50 dark:bg-green-900/10 p-3 rounded">
                    ✅ No upcoming bookings - fully available!
                  </p>
                )}
              </div>
            </div>
          )}

          {selectedDesk.zone.description && (
            <div>
              <p className="text-sm font-medium mb-2">Zone Description</p>
              <p className="text-sm text-muted-foreground leading-relaxed">
                {selectedDesk.zone.description}
              </p>
            </div>
          )}
        </div>

        <DialogFooter className="flex-col gap-2 sm:flex-row pt-4">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="w-full sm:w-auto">
            Close
          </Button>
          {statusForSelectedDate === 'available' && (
            <Button onClick={onBookDesk} className="w-full sm:w-auto">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Book for {shortDateStr}</span>
              <span className="sm:hidden">Book {format(selectedDate, 'MMM dd')}</span>
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 