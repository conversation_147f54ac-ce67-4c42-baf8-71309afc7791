-- Fresh database functions with improved semicolon handling
-- Run this if the previous functions didn't update correctly

-- Drop existing functions first
DROP FUNCTION IF EXISTS execute_query(TEXT);
DROP FUNCTION IF EXISTS execute_query_with_rls(TEXT);

-- Create new execute_query function with better semicolon handling
CREATE OR REPLACE FUNCTION execute_query(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    query_result JSONB;
    cleaned_sql TEXT;
BEGIN
    -- Clean the SQL: remove trailing semicolon and whitespace
    cleaned_sql := TRIM(query_sql);
    WHILE cleaned_sql LIKE '%;' LOOP
        cleaned_sql := LEFT(cleaned_sql, LENGTH(cleaned_sql) - 1);
        cleaned_sql := TRIM(cleaned_sql);
    END LOOP;
    
    -- Validate that it's a SELECT query
    IF NOT (TRIM(UPPER(cleaned_sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed';
    END IF;
    
    -- Check for prohibited operations
    IF cleaned_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE|xp_|sp_)' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for SQL injection patterns
    IF cleaned_sql ~* '(--|/\*|\*/|;\s*(DROP|DELETE|UPDATE|INSERT))' THEN
        RAISE EXCEPTION 'Query contains potentially dangerous patterns';
    END IF;
    
    -- Add row limit if not present (max 1000 rows)
    IF NOT (UPPER(cleaned_sql) LIKE '%LIMIT%') THEN
        cleaned_sql := cleaned_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results as JSONB
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', cleaned_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION execute_query(TEXT) TO authenticated;

-- Create new execute_query_with_rls function
CREATE OR REPLACE FUNCTION execute_query_with_rls(query_sql TEXT)
RETURNS TABLE(result JSONB)
LANGUAGE plpgsql
SECURITY INVOKER
AS $$
DECLARE
    query_result JSONB;
    cleaned_sql TEXT;
    current_user_id UUID;
    current_user_role TEXT;
BEGIN
    -- Get current user info
    SELECT auth.uid() INTO current_user_id;
    SELECT role FROM users WHERE id = current_user_id INTO current_user_role;
    
    -- Clean the SQL: remove trailing semicolon and whitespace
    cleaned_sql := TRIM(query_sql);
    WHILE cleaned_sql LIKE '%;' LOOP
        cleaned_sql := LEFT(cleaned_sql, LENGTH(cleaned_sql) - 1);
        cleaned_sql := TRIM(cleaned_sql);
    END LOOP;
    
    -- Validate that it's a SELECT query
    IF NOT (TRIM(UPPER(cleaned_sql)) LIKE 'SELECT%') THEN
        RAISE EXCEPTION 'Only SELECT queries are allowed';
    END IF;
    
    -- Check for prohibited operations
    IF cleaned_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE|xp_|sp_)' THEN
        RAISE EXCEPTION 'Query contains prohibited operations';
    END IF;
    
    -- Check for SQL injection patterns
    IF cleaned_sql ~* '(--|/\*|\*/|;\s*(DROP|DELETE|UPDATE|INSERT))' THEN
        RAISE EXCEPTION 'Query contains potentially dangerous patterns';
    END IF;
    
    -- For employees, ensure they can only see their own data
    IF current_user_role = 'employee' THEN
        IF NOT (cleaned_sql ~* 'user_id\s*=|users\.id\s*=') THEN
            RAISE EXCEPTION 'Employee queries must include user restrictions';
        END IF;
    END IF;
    
    -- Add row limit if not present (max 1000 rows)
    IF NOT (UPPER(cleaned_sql) LIKE '%LIMIT%') THEN
        cleaned_sql := cleaned_sql || ' LIMIT 1000';
    END IF;
    
    -- Execute the query and return results as JSONB
    EXECUTE format('SELECT json_agg(row_to_json(t)) FROM (%s) t', cleaned_sql) INTO query_result;
    
    -- Return results
    RETURN QUERY SELECT COALESCE(query_result, '[]'::JSONB);
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Query execution failed: %', SQLERRM;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION execute_query_with_rls(TEXT) TO authenticated;

-- Test the functions
SELECT 'Testing execute_query function with semicolon:' as test_description;
SELECT execute_query('SELECT 1 as test_value;');

SELECT 'Testing execute_query function without semicolon:' as test_description;
SELECT execute_query('SELECT 1 as test_value'); 