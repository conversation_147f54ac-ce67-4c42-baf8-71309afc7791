-- Debug version to see what's causing the prohibited operations error

CREATE OR <PERSON><PERSON>LACE FUNCTION debug_prohibited_operations(query_sql TEXT)
RETURNS TABLE(
    step TEXT,
    value TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    cleaned_sql TEXT;
    normalized_sql TEXT;
    prohibited_match BOOLEAN;
BEGIN
    -- Return the original SQL
    RETURN QUERY SELECT 'original_sql'::TEXT, query_sql;
    
    -- Clean the SQL
    cleaned_sql := query_sql;
    cleaned_sql := REPLACE(cleaned_sql, chr(65279), ''); -- BOM
    cleaned_sql := REPLACE(cleaned_sql, chr(8203), ''); -- Zero-width space
    cleaned_sql := REPLACE(cleaned_sql, chr(160), ' '); -- Non-breaking space
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, '\s+', ' ', 'g');
    cleaned_sql := TRIM(cleaned_sql);
    cleaned_sql := REGEXP_REPLACE(cleaned_sql, ';+\s*$', '');
    cleaned_sql := TRIM(cleaned_sql);
    
    RETURN QUERY SELECT 'cleaned_sql'::TEXT, cleaned_sql;
    
    -- Create normalized version
    normalized_sql := UPPER(REGEXP_REPLACE(cleaned_sql, '\s+', '', 'g'));
    RETURN QUERY SELECT 'normalized_sql'::TEXT, normalized_sql;
    
    -- Check each prohibited operation individually
    RETURN QUERY SELECT 'contains_drop'::TEXT, (normalized_sql ~* 'DROP')::TEXT;
    RETURN QUERY SELECT 'contains_delete'::TEXT, (normalized_sql ~* 'DELETE')::TEXT;
    RETURN QUERY SELECT 'contains_update'::TEXT, (normalized_sql ~* 'UPDATE')::TEXT;
    RETURN QUERY SELECT 'contains_insert'::TEXT, (normalized_sql ~* 'INSERT')::TEXT;
    RETURN QUERY SELECT 'contains_create'::TEXT, (normalized_sql ~* 'CREATE')::TEXT;
    RETURN QUERY SELECT 'contains_alter'::TEXT, (normalized_sql ~* 'ALTER')::TEXT;
    RETURN QUERY SELECT 'contains_truncate'::TEXT, (normalized_sql ~* 'TRUNCATE')::TEXT;
    RETURN QUERY SELECT 'contains_exec'::TEXT, (normalized_sql ~* 'EXEC')::TEXT;
    RETURN QUERY SELECT 'contains_execute'::TEXT, (normalized_sql ~* 'EXECUTE')::TEXT;
    
    -- Check the full pattern
    prohibited_match := normalized_sql ~* '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE)';
    RETURN QUERY SELECT 'prohibited_match'::TEXT, prohibited_match::TEXT;
    
    -- Show where the match occurs
    RETURN QUERY SELECT 'match_position'::TEXT, 
        CASE 
            WHEN prohibited_match THEN 
                SUBSTRING(normalized_sql FROM '(DROP|DELETE|UPDATE|INSERT|CREATE|ALTER|TRUNCATE|EXEC|EXECUTE)')
            ELSE 'no_match'
        END;
    
END;
$$;

-- Test with the failing SQL
SELECT * FROM debug_prohibited_operations('SELECT u.department, COUNT(b.id) AS booking_count FROM bookings b JOIN users u ON b.user_id = u.id GROUP BY u.department ORDER BY booking_count DESC;'); 