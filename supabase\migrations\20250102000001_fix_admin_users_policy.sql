/*
  # Fix admin users SELECT policy

  1. Problem
    - <PERSON><PERSON> cannot see all users due to RLS restrictions
    - Current policy causes infinite recursion when checking admin role
    
  2. Solution
    - Drop the existing restrictive "Users can read own data" policy
    - Create a single comprehensive policy that handles both cases
    - Use auth metadata to identify admin users (set during sign-up)
    
  3. Security
    - Admin users can see all users  
    - Regular users can only see their own data
*/

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Users can read own data" ON users;
DROP POLICY IF EXISTS "Admin users can read all data" ON users;
DROP POLICY IF EXISTS "Admin users can select all users" ON users;

-- Create a single comprehensive SELECT policy
CREATE POLICY "Users select policy"
  ON users
  FOR SELECT
  TO authenticated
  USING (
    -- Users can always see their own data
    auth.uid() = id
    OR
    -- Admin users can see all data (check via auth metadata to avoid recursion)
    (
      auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
      OR 
      auth.jwt() ->> 'app_metadata' ->> 'role' = 'admin'
    )
  ); 