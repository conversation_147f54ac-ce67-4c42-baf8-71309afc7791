import { GoogleGenerativeAI } from '@google/generative-ai';

// Initialize Gemini API client
const genAI = new GoogleGenerativeAI(import.meta.env.VITE_GEMINI_API_KEY);

export interface QueryResult {
  type: 'table' | 'chart' | 'metric';
  title: string;
  data: any[];
  chartType?: 'bar' | 'line' | 'pie' | 'area';
  columns?: string[];
  summary?: string;
  sql?: string;
  error?: string;
}

export interface GeminiQueryRequest {
  query: string;
  userId?: string;
  userRole?: 'employee' | 'manager' | 'admin';
}

const DATABASE_SCHEMA = `
Database Schema for SymDesk Workspace Management System:

TABLES:
1. users
   - id (uuid, primary key)
   - email (text, unique)
   - name (text)
   - role (enum: employee, manager, admin)
   - avatar (text, optional)
   - phone (text, optional)
   - bio (text, optional)
   - location (text, optional)
   - department (text, optional)
   - manager_id (uuid, references users.id)
   - created_at (timestamp)
   - updated_at (timestamp)

2. zones
   - id (uuid, primary key)
   - name (text)
   - floor_number (integer)
   - description (text, optional)
   - photo (text, optional)
   - created_at (timestamp)
   - updated_at (timestamp)

3. desks
   - id (uuid, primary key)
   - name (text)
   - status (enum: available, occupied, maintenance)
   - zone_id (uuid, references zones.id)
   - coordinates (json: {x: number, y: number})
   - created_at (timestamp)
   - updated_at (timestamp)

4. bookings
   - id (uuid, primary key)
   - user_id (uuid, references users.id)
   - desk_id (uuid, references desks.id)
   - date (date)
   - end_date (date)
   - start_time (time)
   - end_time (time)
   - status (enum: booked, checked-in, cancelled)
   - created_at (timestamp)
   - updated_at (timestamp)

5. settings
   - id (uuid, primary key)
   - company_policies (json)
   - working_hours (json: {start: string, end: string})
   - booking_restrictions (json)
   - created_at (timestamp)
   - updated_at (timestamp)

RELATIONSHIPS:
- users.manager_id → users.id (manager relationship)
- desks.zone_id → zones.id (desk belongs to zone)
- bookings.user_id → users.id (booking belongs to user)
- bookings.desk_id → desks.id (booking belongs to desk)

BUSINESS RULES:
- Each user has a role: employee, manager, or admin
- Managers can see their team's data
- Admins can see all data
- Bookings can span multiple days (date to end_date)
- Bookings have specific time slots (start_time to end_time)
- Desks have physical coordinates for floor plan visualization
- Zones represent areas/floors in the building
`;

const QUERY_GUIDELINES = `
QUERY GUIDELINES:
1. Generate PostgreSQL-compatible SQL queries
2. Always use proper JOINs for related data
3. Include appropriate WHERE clauses for data filtering
4. Use date functions for time-based queries
5. Consider user roles for data access permissions
6. Return meaningful column names
7. Use aggregation functions for summary statistics
8. Format dates and times appropriately
9. Handle NULL values properly
10. Optimize queries for performance
11. **ALWAYS use case-insensitive string comparisons**

CASE-INSENSITIVE SEARCH PATTERNS:
- Use ILIKE instead of LIKE for pattern matching: WHERE name ILIKE '%john%'
- Use UPPER() or LOWER() for exact matches: WHERE UPPER(department) = UPPER('engineering')
- Use ILIKE for contains searches: WHERE email ILIKE '%@company.com'
- Use ~* for regex patterns: WHERE name ~* '^john'
- For equality comparisons: WHERE LOWER(status) = LOWER('active')

EXAMPLES:
- Find users by name: WHERE UPPER(name) = UPPER('John Doe')
- Find by department: WHERE UPPER(department) = UPPER('Engineering')
- Search in descriptions: WHERE description ILIKE '%meeting%'
- Filter by status: WHERE LOWER(status) = LOWER('Available')
- Search by email: WHERE email ILIKE '%@company.com'
- Find desk names: WHERE UPPER(desk_name) = UPPER('Desk A1')

COMMON QUERY PATTERNS:
- Desk utilization: COUNT bookings by desk/zone/date
- User activity: COUNT bookings by user/department
- Time analysis: GROUP BY date, time periods
- Space analysis: JOIN desks, zones, bookings
- Team analysis: JOIN users with manager relationships
- Trend analysis: Compare time periods

SECURITY CONSIDERATIONS:
- Always validate user permissions
- Use parameterized queries to prevent SQL injection
- Limit result sets to prevent overwhelming responses
- Sanitize input parameters
`;

export class GeminiQueryService {
  private model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash-preview-04-17' });

  async processNaturalLanguageQuery(request: GeminiQueryRequest): Promise<{ sql: string; error?: string }> {
    try {
      const { query, userRole = 'employee' } = request;

      // Generate SQL query using Gemini
      const sqlQuery = await this.generateSQLQuery(query, userRole);
      
      // Validate the generated SQL
      this.validateSQL(sqlQuery);

      // Return the SQL for execution by AIQueryService
      return { sql: sqlQuery };
    } catch (error) {
      console.error('Error processing natural language query:', error);
      return {
        sql: '',
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async generateSQLQuery(query: string, userRole: string): Promise<string> {
    const roleContext = this.getRoleContext(userRole);
    
    const prompt = `
${DATABASE_SCHEMA}

${QUERY_GUIDELINES}

USER ROLE: ${userRole}
${roleContext}

NATURAL LANGUAGE QUERY: "${query}"

Based on the database schema above, generate a PostgreSQL SQL query that answers the user's question.
The query should:
1. Be syntactically correct PostgreSQL
2. Include proper JOINs for related data
3. Respect user role permissions
4. Return meaningful column names
5. Include appropriate WHERE clauses and aggregations
6. Handle edge cases and NULL values
7. Be a SELECT query only (no INSERT, UPDATE, DELETE, DROP, etc.)
8. Not include SQL comments or markdown formatting

IMPORTANT: Return ONLY the raw SQL query without any explanations, comments, or markdown formatting like \`\`\`sql. Just the plain SQL query.
`;

    const result = await this.model.generateContent(prompt);
    const response = await result.response;
    const rawSQL = response.text().trim();

    // Clean the SQL by removing markdown formatting
    const cleanedSQL = rawSQL
      .replace(/```sql/gi, '')      // Remove ```sql
      .replace(/```/g, '')          // Remove ```
      .replace(/\n\s*\n/g, '\n')    // Remove empty lines
      .trim();

    console.log('Raw SQL from Gemini:', rawSQL);
    console.log('Cleaned SQL from Gemini:', cleanedSQL);
    console.log('Gemini SQL has semicolon:', cleanedSQL.includes(';'));
    console.log('Gemini SQL has LIMIT:', cleanedSQL.toUpperCase().includes('LIMIT'));

    return cleanedSQL;
  }

  private getRoleContext(userRole: string): string {
    switch (userRole) {
      case 'admin':
        return 'PERMISSIONS: Can access all data across the organization';
      case 'manager':
        return 'PERMISSIONS: Can access data for their team members and subordinates';
      case 'employee':
        return 'PERMISSIONS: Can access only their own data and general statistics';
      default:
        return 'PERMISSIONS: Limited access to general statistics only';
    }
  }

  private validateSQL(sql: string): void {
    console.log('Validating SQL:', sql);
    console.log('SQL has semicolon:', sql.includes(';'));
    console.log('SQL has LIMIT:', sql.toUpperCase().includes('LIMIT'));
    
    // Clean SQL by removing markdown, quotes, and normalize spaces
    const cleanedSQL = sql
      .replace(/```sql/gi, '')      // Remove ```sql
      .replace(/```/g, '')          // Remove ```
      .replace(/'/g, '')            // Remove single quotes
      .replace(/"/g, '')            // Remove double quotes
      .replace(/\s+/g, ' ')         // Normalize spaces
      .trim();
    
    // More precise prohibited patterns - only check for actual SQL operations, not column names
    const prohibitedPatterns = [
      /^DROP\s+TABLE/i,           // Must be at start of statement
      /^DELETE\s+FROM/i,          // Must be at start of statement
      /^UPDATE\s+\w+\s+SET/i,     // Must be at start of statement with SET
      /^INSERT\s+INTO/i,          // Must be at start of statement
      /^CREATE\s+TABLE/i,         // Must be at start of statement
      /^ALTER\s+TABLE/i,          // Must be at start of statement
      /^TRUNCATE\s+TABLE/i,       // Must be at start of statement
      /^EXEC\s/i,                 // Must be at start of statement
      /^EXECUTE\s/i,              // Must be at start of statement
      /;\s*DROP\s+TABLE/i,        // After semicolon
      /;\s*DELETE\s+FROM/i,       // After semicolon
      /;\s*UPDATE\s+\w+\s+SET/i,  // After semicolon
      /;\s*INSERT\s+INTO/i,       // After semicolon
      /xp_cmdshell/i,             // SQL Server attack
      /sp_executesql/i,           // SQL Server attack
      /--.*DROP/i,                // Comment with DROP
      /--.*DELETE/i,              // Comment with DELETE
      /\/\*.*DROP.*\*\//i,        // Comment block with DROP
      /\/\*.*DELETE.*\*\//i       // Comment block with DELETE
    ];

    for (const pattern of prohibitedPatterns) {
      if (pattern.test(cleanedSQL)) {
        console.error('SQL validation failed for pattern:', pattern, 'in SQL:', cleanedSQL);
        throw new Error('Invalid SQL query: Contains prohibited operations');
      }
    }

    // Ensure it's a SELECT query (allow WITH clauses)
    const trimmedSQL = cleanedSQL.trim().toUpperCase();
    if (!trimmedSQL.startsWith('SELECT') && !trimmedSQL.startsWith('WITH')) {
      console.error('SQL validation failed: Not a SELECT query:', trimmedSQL);
      throw new Error('Only SELECT queries are allowed');
    }
    
    console.log('SQL validation passed');
  }

  private generateMockResult(query: string, sql: string): QueryResult {
    const lowercaseQuery = query.toLowerCase();

    // Determine result type based on query content
    if (lowercaseQuery.includes('count') || lowercaseQuery.includes('total') || lowercaseQuery.includes('number')) {
      return {
        type: 'metric',
        title: 'Query Result',
        data: [{ value: 42, label: 'Total Count' }],
        sql,
        summary: 'This metric shows the total count based on your query.'
      };
    }

    if (lowercaseQuery.includes('trend') || lowercaseQuery.includes('over time') || lowercaseQuery.includes('by date')) {
      return {
        type: 'chart',
        title: 'Trend Analysis',
        data: [
          { date: '2024-01-01', value: 25 },
          { date: '2024-01-02', value: 30 },
          { date: '2024-01-03', value: 35 },
          { date: '2024-01-04', value: 28 },
          { date: '2024-01-05', value: 45 }
        ],
        chartType: 'line',
        sql,
        summary: 'This chart shows the trend over time based on your query.'
      };
    }

    if (lowercaseQuery.includes('department') || lowercaseQuery.includes('by department')) {
      return {
        type: 'chart',
        title: 'Department Analysis',
        data: [
          { department: 'Engineering', value: 45 },
          { department: 'Sales', value: 32 },
          { department: 'Marketing', value: 28 },
          { department: 'HR', value: 15 }
        ],
        chartType: 'bar',
        sql,
        summary: 'This chart shows the breakdown by department.'
      };
    }

    // Default table result
    return {
      type: 'table',
      title: 'Query Results',
      data: [
        { id: 1, name: 'John Doe', department: 'Engineering', bookings: 15 },
        { id: 2, name: 'Jane Smith', department: 'Sales', bookings: 12 },
        { id: 3, name: 'Bob Johnson', department: 'Marketing', bookings: 8 }
      ],
      columns: ['ID', 'Name', 'Department', 'Bookings'],
      sql,
      summary: 'This table shows the results of your query.'
    };
  }
} 