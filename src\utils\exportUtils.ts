import { format } from 'date-fns';

export interface ExportData {
  filename: string;
  data: any[];
  headers: string[];
}

export function downloadCSV(exportData: ExportData) {
  const { filename, data, headers } = exportData;
  
  // Create CSV content
  const csvContent = [
    headers.join(','),
    ...data.map(row => 
      headers.map(header => {
        const value = row[header] || '';
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');

  // Create and download file
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}_${format(new Date(), 'yyyy-MM-dd')}.csv`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

export function exportUtilizationData(data: any[], dateRange: string) {
  downloadCSV({
    filename: `utilization_report_${dateRange}`,
    data,
    headers: ['date', 'utilization', 'totalBookings', 'totalDesks']
  });
}

export function exportDeskUsageData(data: any[], dateRange: string) {
  downloadCSV({
    filename: `desk_usage_report_${dateRange}`,
    data,
    headers: ['deskName', 'zoneName', 'floorNumber', 'bookingCount', 'utilizationRate']
  });
}

export function exportDepartmentData(data: any[], dateRange: string) {
  downloadCSV({
    filename: `department_report_${dateRange}`,
    data,
    headers: ['department', 'bookingCount', 'userCount', 'avgBookingsPerUser']
  });
}

export function exportZoneData(data: any[], dateRange: string) {
  downloadCSV({
    filename: `zone_report_${dateRange}`,
    data,
    headers: ['zoneName', 'floorNumber', 'totalDesks', 'bookingCount', 'utilizationRate']
  });
}
